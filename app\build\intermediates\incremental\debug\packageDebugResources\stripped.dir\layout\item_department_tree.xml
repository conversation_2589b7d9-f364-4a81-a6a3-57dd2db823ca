<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_item_normal"
    android:clickable="true"
    android:focusable="true"
    android:orientation="horizontal"
    android:paddingVertical="8dp">

    <!-- 缩进视图 -->
    <View
        android:id="@+id/view_indent"
        android:layout_width="0dp"
        android:layout_height="match_parent" />

    <!-- 展开/折叠图标 -->
    <ImageView
        android:id="@+id/iv_expand"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="8dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:padding="4dp"
        android:src="@drawable/ic_expand_more"
        app:tint="@color/text_secondary" />

    <!-- 部门图标 -->
    <ImageView
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="8dp"
        android:src="@drawable/ic_business"
        app:tint="@color/primary" />

    <!-- 部门信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            tools:text="技术部" />

        <TextView
            android:id="@+id/tv_code"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            tools:text="TECH001" />

    </LinearLayout>

    <!-- 选中指示器 -->
    <View
        android:id="@+id/view_selected_indicator"
        android:layout_width="4dp"
        android:layout_height="match_parent"
        android:layout_marginEnd="8dp"
        android:background="@color/primary"
        android:visibility="gone" />

</LinearLayout>
