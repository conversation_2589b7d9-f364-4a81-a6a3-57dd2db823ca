package com.opms.ui.business.dialog;

import com.opms.data.repository.ComponentRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ComponentSelectionDialog_MembersInjector implements MembersInjector<ComponentSelectionDialog> {
  private final Provider<ComponentRepository> componentRepositoryProvider;

  public ComponentSelectionDialog_MembersInjector(
      Provider<ComponentRepository> componentRepositoryProvider) {
    this.componentRepositoryProvider = componentRepositoryProvider;
  }

  public static MembersInjector<ComponentSelectionDialog> create(
      Provider<ComponentRepository> componentRepositoryProvider) {
    return new ComponentSelectionDialog_MembersInjector(componentRepositoryProvider);
  }

  @Override
  public void injectMembers(ComponentSelectionDialog instance) {
    injectComponentRepository(instance, componentRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.opms.ui.business.dialog.ComponentSelectionDialog.componentRepository")
  public static void injectComponentRepository(ComponentSelectionDialog instance,
      ComponentRepository componentRepository) {
    instance.componentRepository = componentRepository;
  }
}
