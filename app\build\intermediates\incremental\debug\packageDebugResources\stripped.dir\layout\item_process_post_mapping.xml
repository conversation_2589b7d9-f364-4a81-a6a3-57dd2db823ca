<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:rippleColor="@color/primary_variant">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- 流程信息 -->
        <TextView
            android:id="@+id/tv_process_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="流程："
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_process_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/btn_delete"
            app:layout_constraintStart_toEndOf="@id/tv_process_label"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="生产流程A" />

        <TextView
            android:id="@+id/tv_process_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@id/btn_delete"
            app:layout_constraintStart_toEndOf="@id/tv_process_label"
            app:layout_constraintTop_toBottomOf="@id/tv_process_name"
            tools:text="PROC001" />

        <!-- 分隔线 -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="12dp"
            android:background="@color/divider"
            app:layout_constraintTop_toBottomOf="@id/tv_process_code" />

        <!-- 岗位信息 -->
        <TextView
            android:id="@+id/tv_post_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:text="岗位："
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider" />

        <TextView
            android:id="@+id/tv_post_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="12dp"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@id/btn_delete"
            app:layout_constraintStart_toEndOf="@id/tv_post_label"
            app:layout_constraintTop_toBottomOf="@id/divider"
            tools:text="操作员" />

        <TextView
            android:id="@+id/tv_post_code"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintEnd_toStartOf="@id/btn_delete"
            app:layout_constraintStart_toEndOf="@id/tv_post_label"
            app:layout_constraintTop_toBottomOf="@id/tv_post_name"
            tools:text="POST001" />

        <!-- 备注信息 -->
        <TextView
            android:id="@+id/tv_remark"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:ellipsize="end"
            android:maxLines="2"
            android:text="备注：这是一个测试备注信息"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintEnd_toStartOf="@id/btn_delete"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_post_code"
            tools:visibility="visible" />

        <!-- 状态指示器 -->
        <View
            android:id="@+id/status_indicator"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/circle_green"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_remark" />

        <TextView
            android:id="@+id/tv_status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:text="启用"
            android:textColor="@color/text_secondary"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/status_indicator"
            app:layout_constraintTop_toBottomOf="@id/tv_remark" />

        <!-- 删除按钮 -->
        <ImageButton
            android:id="@+id/btn_delete"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="删除映射"
            android:src="@drawable/ic_delete"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/error" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
