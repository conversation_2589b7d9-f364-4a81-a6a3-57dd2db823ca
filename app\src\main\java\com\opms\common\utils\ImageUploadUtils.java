package com.opms.common.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.opms.common.enums.BusinessImgType;
import com.opms.data.local.PreferencesManager;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.remote.ApiService;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * 通用图片上传工具类
 * 支持不同业务模块的图片上传需求
 */
public class ImageUploadUtils {

    private static final String TAG = "ImageUploadUtils";

    /**
     * 上传图片（从URI）
     *
     * @param context      上下文
     * @param apiService   API服务
     * @param imageUri     图片URI
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    public static void uploadImage(Context context,
                                   ApiService apiService,
                                   Uri imageUri,
                                   BusinessImgType businessType,
                                   String businessId,
                                   String operator,
                                   ImageUploadCallback callback) {

        Log.d(TAG, "开始上传图片: businessType=" + businessType.getCode() +
                ", businessId=" + businessId + ", operator=" + operator);

        if (callback != null) {
            callback.onUploadStart();
        }

        try {
            // 将URI转换为Bitmap
            Bitmap bitmap = MediaStore.Images.Media.getBitmap(context.getContentResolver(), imageUri);

            // 创建临时文件
            File tempFile = createTempImageFile(context, bitmap, businessType, businessId);

            // 上传文件
            uploadImageFile(apiService, tempFile, businessType, businessId, operator, callback);

        } catch (IOException e) {
            Log.e(TAG, "处理图片失败: " + e.getMessage(), e);
            if (callback != null) {
                callback.onUploadFailure("处理图片失败: " + e.getMessage());
                callback.onUploadComplete();
            }
        }
    }

    /**
     * 上传图片（从文件）
     *
     * @param apiService   API服务
     * @param imageFile    图片文件
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    public static void uploadImageFile(ApiService apiService,
                                       File imageFile,
                                       BusinessImgType businessType,
                                       String businessId,
                                       String operator,
                                       ImageUploadCallback callback) {

        Log.d(TAG, "上传图片文件: " + imageFile.getAbsolutePath());

        try {
            // 创建请求参数
            RequestBody businessTypeBody = RequestBody.create(MediaType.parse("text/plain"), businessType.getCode());
            RequestBody businessIdBody = RequestBody.create(MediaType.parse("text/plain"), businessId);
            RequestBody operatorBody = RequestBody.create(MediaType.parse("text/plain"), operator);

            // 创建文件的MultipartBody.Part
            RequestBody requestFile = RequestBody.create(MediaType.parse("image/jpeg"), imageFile);
            MultipartBody.Part filePart = MultipartBody.Part.createFormData("image", imageFile.getName(), requestFile);

            // 调用API
            Call<ApiResponse<String>> call = apiService.uploadImage(businessTypeBody, businessIdBody, operatorBody, filePart);
            call.enqueue(new Callback<ApiResponse<String>>() {
                @Override
                public void onResponse(@NonNull Call<ApiResponse<String>> call,
                                       @NonNull Response<ApiResponse<String>> response) {

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<String> apiResponse = response.body();
                        if (apiResponse.isSuccess()) {
                            Log.d(TAG, "图片上传成功: " + apiResponse.getData());
                            if (callback != null) {
                                callback.onUploadSuccess(apiResponse.getData());
                            }
                        } else {
                            String errorMsg = apiResponse.getMessage() != null ? apiResponse.getMessage() : "图片上传失败";
                            Log.e(TAG, "图片上传失败: " + errorMsg);
                            if (callback != null) {
                                callback.onUploadFailure(errorMsg);
                            }
                        }
                    } else {
                        Log.e(TAG, "图片上传失败: HTTP " + response.code());
                        if (callback != null) {
                            callback.onUploadFailure("图片上传失败");
                        }
                    }

                    // 清理临时文件
                    if (imageFile.exists()) {
                        boolean deleted = imageFile.delete();
                        Log.d(TAG, "临时文件删除: " + deleted);
                    }

                    if (callback != null) {
                        callback.onUploadComplete();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<ApiResponse<String>> call, @NonNull Throwable t) {
                    Log.e(TAG, "图片上传网络错误: " + t.getMessage(), t);

                    // 清理临时文件
                    if (imageFile.exists()) {
                        boolean deleted = imageFile.delete();
                        Log.d(TAG, "临时文件删除: " + deleted);
                    }

                    if (callback != null) {
                        callback.onUploadFailure("网络错误: " + t.getMessage());
                        callback.onUploadComplete();
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "创建上传请求失败: " + e.getMessage(), e);
            if (callback != null) {
                callback.onUploadFailure("创建上传请求失败: " + e.getMessage());
                callback.onUploadComplete();
            }
        }
    }

    /**
     * 批量上传图片（从URI列表）
     *
     * @param context      上下文
     * @param apiService   API服务
     * @param imageUris    图片URI列表
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param callback     回调接口
     */
    public static void uploadMultipleImages(Context context,
                                            ApiService apiService,
                                            List<Uri> imageUris,
                                            BusinessImgType businessType,
                                            String businessId,
                                            String operator,
                                            MultiImageUploadCallback callback) {

        Log.d(TAG, "开始批量上传图片: businessType=" + businessType.getCode() +
                ", businessId=" + businessId + ", operator=" + operator + ", 图片数量=" + imageUris.size());

        if (callback != null) {
            callback.onUploadStart();
        }

        if (imageUris == null || imageUris.isEmpty()) {
            if (callback != null) {
                callback.onUploadComplete(new ArrayList<>(), new ArrayList<>());
            }
            return;
        }

        List<String> successUrls = new ArrayList<>();
        List<String> failureMessages = new ArrayList<>();
        AtomicInteger completedCount = new AtomicInteger(0);
        int totalCount = imageUris.size();

        for (int i = 0; i < imageUris.size(); i++) {
            Uri imageUri = imageUris.get(i);
            int currentIndex = i;

            try {
                // 将URI转换为Bitmap
                Bitmap bitmap = MediaStore.Images.Media.getBitmap(context.getContentResolver(), imageUri);

                // 创建临时文件
                File tempFile = createTempImageFile(context, bitmap, businessType, businessId + "_" + currentIndex);

                // 上传单个文件
                uploadImageFile(apiService, tempFile, businessType, businessId, operator, new ImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        // 单个图片开始上传
                    }

                    @Override
                    public void onUploadSuccess(String imageUrl) {
                        synchronized (successUrls) {
                            successUrls.add(imageUrl);
                        }
                        if (callback != null) {
                            callback.onSingleImageSuccess(imageUrl);
                            callback.onUploadProgress(completedCount.incrementAndGet(), totalCount);
                        }
                        checkAllCompleted();
                    }

                    @Override
                    public void onUploadFailure(String errorMessage) {
                        synchronized (failureMessages) {
                            failureMessages.add("图片 " + (currentIndex + 1) + ": " + errorMessage);
                        }
                        if (callback != null) {
                            callback.onSingleImageFailure(errorMessage);
                            callback.onUploadProgress(completedCount.incrementAndGet(), totalCount);
                        }
                        checkAllCompleted();
                    }

                    @Override
                    public void onUploadComplete() {
                        // 单个图片上传完成
                    }

                    private void checkAllCompleted() {
                        if (completedCount.get() == totalCount) {
                            if (callback != null) {
                                callback.onUploadComplete(successUrls, failureMessages);
                            }
                        }
                    }
                });

            } catch (IOException e) {
                Log.e(TAG, "处理图片失败: " + e.getMessage(), e);
                synchronized (failureMessages) {
                    failureMessages.add("图片 " + (currentIndex + 1) + ": 处理失败 - " + e.getMessage());
                }
                if (callback != null) {
                    callback.onSingleImageFailure("处理失败: " + e.getMessage());
                    callback.onUploadProgress(completedCount.incrementAndGet(), totalCount);
                }

                // 检查是否全部完成
                if (completedCount.get() == totalCount) {
                    if (callback != null) {
                        callback.onUploadComplete(successUrls, failureMessages);
                    }
                }
            }
        }
    }

    /**
     * 删除图片
     *
     * @param apiService   API服务
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param operator     操作人
     * @param imageId     要删除的图片ID
     * @param callback     回调接口
     */
    public static void deleteImage(ApiService apiService,
                                   BusinessImgType businessType,
                                   String businessId,
                                   String operator,
                                   String imageId,
                                   ImageDeleteCallback callback) {

        Log.d(TAG, "删除图片: businessType=" + businessType.getCode() +
                ", businessId=" + businessId + ", imageId=" + imageId);

        if (callback != null) {
            callback.onDeleteStart();
        }

        try {
            Call<ApiResponse<Void>> call = apiService.deleteImage(
                    businessType.getCode(),
                    businessId,
                    operator,
                    imageId
            );

            call.enqueue(new Callback<ApiResponse<Void>>() {
                @Override
                public void onResponse(@NonNull Call<ApiResponse<Void>> call,
                                       @NonNull Response<ApiResponse<Void>> response) {

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<Void> apiResponse = response.body();
                        if (apiResponse.isSuccess()) {
                            Log.d(TAG, "图片删除成功");
                            if (callback != null) {
                                callback.onDeleteSuccess();
                            }
                        } else {
                            String errorMsg = apiResponse.getMessage() != null ? apiResponse.getMessage() : "图片删除失败";
                            Log.e(TAG, "图片删除失败: " + errorMsg);
                            if (callback != null) {
                                callback.onDeleteFailure(errorMsg);
                            }
                        }
                    } else {
                        Log.e(TAG, "图片删除失败: HTTP " + response.code());
                        if (callback != null) {
                            callback.onDeleteFailure("图片删除失败");
                        }
                    }

                    if (callback != null) {
                        callback.onDeleteComplete();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<ApiResponse<Void>> call, @NonNull Throwable t) {
                    Log.e(TAG, "图片删除网络错误: " + t.getMessage(), t);

                    if (callback != null) {
                        callback.onDeleteFailure("网络错误: " + t.getMessage());
                        callback.onDeleteComplete();
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "创建删除请求失败: " + e.getMessage(), e);
            if (callback != null) {
                callback.onDeleteFailure("创建删除请求失败: " + e.getMessage());
                callback.onDeleteComplete();
            }
        }
    }

    /**
     * 获取业务对象的所有图片
     *
     * @param apiService   API服务
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @param callback     回调接口
     */
    public static void getImages(ApiService apiService,
                                 BusinessImgType businessType,
                                 String businessId,
                                 ImageListCallback callback) {

        Log.d(TAG, "获取图片列表: businessType=" + businessType.getCode() + ", businessId=" + businessId);

        if (callback != null) {
            callback.onLoadStart();
        }

        try {
            Call<ApiResponse<List<ImageResponse>>> call = apiService.getImages(
                    businessType.getCode(),
                    businessId
            );

            call.enqueue(new Callback<ApiResponse<List<ImageResponse>>>() {
                @Override
                public void onResponse(@NonNull Call<ApiResponse<List<ImageResponse>>> call,
                                       @NonNull Response<ApiResponse<List<ImageResponse>>> response) {

                    if (response.isSuccessful() && response.body() != null) {
                        ApiResponse<List<ImageResponse>> apiResponse = response.body();
                        if (apiResponse.isSuccess()) {
                            List<ImageResponse> imageResponse = apiResponse.getData() != null ? apiResponse.getData() : new ArrayList<>();
                            Log.d(TAG, "获取图片列表成功，数量: " + imageResponse.size());
                            if (callback != null) {
                                callback.onLoadSuccess(imageResponse);
                            }
                        } else {
                            String errorMsg = apiResponse.getMessage() != null ? apiResponse.getMessage() : "获取图片列表失败";
                            Log.e(TAG, "获取图片列表失败: " + errorMsg);
                            if (callback != null) {
                                callback.onLoadFailure(errorMsg);
                            }
                        }
                    } else {
                        Log.e(TAG, "获取图片列表失败: HTTP " + response.code());
                        if (callback != null) {
                            callback.onLoadFailure("获取图片列表失败");
                        }
                    }

                    if (callback != null) {
                        callback.onLoadComplete();
                    }
                }

                @Override
                public void onFailure(@NonNull Call<ApiResponse<List<ImageResponse>>> call, @NonNull Throwable t) {
                    Log.e(TAG, "获取图片列表网络错误: " + t.getMessage(), t);

                    if (callback != null) {
                        callback.onLoadFailure("网络错误: " + t.getMessage());
                        callback.onLoadComplete();
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "创建获取图片列表请求失败: " + e.getMessage(), e);
            if (callback != null) {
                callback.onLoadFailure("创建获取图片列表请求失败: " + e.getMessage());
                callback.onLoadComplete();
            }
        }
    }

    /**
     * 创建临时图片文件
     */
    private static File createTempImageFile(Context context, Bitmap bitmap, BusinessImgType businessType, String businessId) throws IOException {
        // 创建临时文件目录
        File cacheDir = new File(context.getCacheDir(), "temp_images");
        if (!cacheDir.exists()) {
            boolean created = cacheDir.mkdirs();
            Log.d(TAG, "创建临时目录: " + created);
        }

        // 创建临时文件
        String fileName = businessType.getCode() + "_" + businessId + "_" + System.currentTimeMillis() + ".jpg";
        File imageFile = new File(cacheDir, fileName);

        // 保存Bitmap到文件
        FileOutputStream fos = new FileOutputStream(imageFile);
        bitmap.compress(Bitmap.CompressFormat.JPEG, 80, fos);
        fos.close();

        Log.d(TAG, "创建临时文件: " + imageFile.getAbsolutePath() + ", 大小: " + imageFile.length() + " bytes");

        return imageFile;
    }

    /**
     * 获取当前用户名
     * 从PreferencesManager中获取已登录用户的用户名
     */
    public static String getCurrentUser(Context context) {
        try {
            PreferencesManager preferencesManager = new PreferencesManager(context);
            String username = preferencesManager.getUsername();

            if (!TextUtils.isEmpty(username)) {
                Log.d(TAG, "获取当前用户: " + username);
                return username;
            } else {
                Log.w(TAG, "用户未登录，使用默认操作人");
                return "system";
            }
        } catch (Exception e) {
            Log.e(TAG, "获取当前用户失败: " + e.getMessage(), e);
            return "system";
        }
    }

    /**
     * 图片上传回调接口
     */
    public interface ImageUploadCallback {
        void onUploadStart();

        void onUploadSuccess(String imageUrl);

        void onUploadFailure(String errorMessage);

        void onUploadComplete();
    }

    /**
     * 多图片上传回调接口
     */
    public interface MultiImageUploadCallback {
        void onUploadStart();

        void onUploadProgress(int uploadedCount, int totalCount);

        void onSingleImageSuccess(String imageUrl);

        void onSingleImageFailure(String errorMessage);

        void onUploadComplete(List<String> successUrls, List<String> failureMessages);
    }

    /**
     * 图片删除回调接口
     */
    public interface ImageDeleteCallback {
        void onDeleteStart();

        void onDeleteSuccess();

        void onDeleteFailure(String errorMessage);

        void onDeleteComplete();
    }

    /**
     * 图片获取回调接口
     */
    public interface ImageListCallback {
        void onLoadStart();

        void onLoadSuccess(List<ImageResponse> imageUrls);

        void onLoadFailure(String errorMessage);

        void onLoadComplete();
    }

    /**
     * 图片业务ID更新回调接口
     */
    public interface ImageUpdateCallback {
        void onUpdateSuccess();

        void onUpdateFailure(String errorMessage);
    }

    /**
     * 更新图片的业务ID关联
     *
     * @param apiService      API服务
     * @param businessType    业务类型
     * @param oldBusinessId   旧业务ID（临时ID）
     * @param newBusinessId   新业务ID（实际ID）
     * @param operator        操作人
     * @param callback        回调接口
     */
    public static void updateImageBusinessId(ApiService apiService,
                                             BusinessImgType businessType,
                                             String oldBusinessId,
                                             String newBusinessId,
                                             String operator,
                                             ImageUpdateCallback callback) {

        Log.d(TAG, "更新图片业务ID关联: " + businessType.getCode() +
                ", " + oldBusinessId + " -> " + newBusinessId + ", operator=" + operator);

        Call<ApiResponse<Void>> call = apiService.updateImageBusinessId(
                businessType.getCode(),
                oldBusinessId,
                newBusinessId,
                operator
        );

        call.enqueue(new Callback<ApiResponse<Void>>() {
            @Override
            public void onResponse(Call<ApiResponse<Void>> call, Response<ApiResponse<Void>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<Void> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        Log.d(TAG, "图片业务ID更新成功");
                        if (callback != null) {
                            callback.onUpdateSuccess();
                        }
                    } else {
                        String errorMessage = "更新失败: " + apiResponse.getMessage();
                        Log.e(TAG, errorMessage);
                        if (callback != null) {
                            callback.onUpdateFailure(errorMessage);
                        }
                    }
                } else {
                    String errorMessage = "服务器响应错误: " + response.code();
                    Log.e(TAG, errorMessage);
                    if (callback != null) {
                        callback.onUpdateFailure(errorMessage);
                    }
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<Void>> call, Throwable t) {
                String errorMessage = "网络请求失败: " + t.getMessage();
                Log.e(TAG, errorMessage, t);
                if (callback != null) {
                    callback.onUpdateFailure(errorMessage);
                }
            }
        });
    }
}
