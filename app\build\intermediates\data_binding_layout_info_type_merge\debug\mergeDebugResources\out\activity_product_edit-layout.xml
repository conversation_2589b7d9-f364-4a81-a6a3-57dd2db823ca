<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_product_edit" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_product_edit.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_product_edit_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="340" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="13" startOffset="8" endLine="18" endOffset="66"/></Target><Target id="@+id/card_images" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="33" startOffset="12" endLine="66" endOffset="47"/></Target><Target id="@+id/rv_product_images" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="58" startOffset="20" endLine="62" endOffset="64"/></Target><Target id="@+id/card_basic_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="69" startOffset="12" endLine="216" endOffset="47"/></Target><Target id="@+id/et_product_name" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="102" startOffset="24" endLine="107" endOffset="50"/></Target><Target id="@+id/til_product_code" view="com.google.android.material.textfield.TextInputLayout"><Expressions/><location startLine="112" startOffset="20" endLine="127" endOffset="75"/></Target><Target id="@+id/et_product_code" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="120" startOffset="24" endLine="125" endOffset="50"/></Target><Target id="@+id/et_product_model" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="137" startOffset="24" endLine="142" endOffset="50"/></Target><Target id="@+id/et_product_standard" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="154" startOffset="24" endLine="159" endOffset="50"/></Target><Target id="@+id/et_product_price" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="171" startOffset="24" endLine="176" endOffset="50"/></Target><Target id="@+id/spinner_status" view="AutoCompleteTextView"><Expressions/><location startLine="188" startOffset="24" endLine="193" endOffset="47"/></Target><Target id="@+id/et_product_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="204" startOffset="24" endLine="210" endOffset="50"/></Target><Target id="@+id/card_component_info" view="androidx.cardview.widget.CardView"><Expressions/><location startLine="219" startOffset="12" endLine="299" endOffset="47"/></Target><Target id="@+id/btn_add_component" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="252" startOffset="24" endLine="258" endOffset="57"/></Target><Target id="@+id/ll_component_list" view="LinearLayout"><Expressions/><location startLine="263" startOffset="20" endLine="268" endOffset="56"/></Target><Target id="@+id/ll_component_empty" view="LinearLayout"><Expressions/><location startLine="271" startOffset="20" endLine="295" endOffset="34"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="311" startOffset="16" endLine="317" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="319" startOffset="16" endLine="324" endOffset="50"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="333" startOffset="4" endLine="338" endOffset="35"/></Target></Targets></Layout>