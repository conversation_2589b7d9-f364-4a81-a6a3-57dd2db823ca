<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <GridLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:columnCount="2"
        android:rowCount="4"
        app:layout_constraintTop_toTopOf="parent">

        <LinearLayout
            android:id="@+id/ll_user_management"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:layout_margin="8dp"
            android:background="@drawable/bg_menu_item"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_user_management" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="用户管理"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_department_management"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:layout_margin="8dp"
            android:background="@drawable/bg_menu_item"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_department_management" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="部门管理"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_position_management"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:layout_margin="8dp"
            android:background="@drawable/bg_menu_item"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_position_management" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="职位管理"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_job_management"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:layout_margin="8dp"
            android:background="@drawable/bg_menu_item"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_job_management" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="岗位管理"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_process_management"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:layout_margin="8dp"
            android:background="@drawable/bg_menu_item"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_process_management" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="流程管理"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_process_post_mapping"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:layout_margin="8dp"
            android:background="@drawable/bg_menu_item"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_process_post_mapping" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="流程岗位映射"
                android:textSize="16sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/ll_permission_management"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_columnWeight="1"
            android:layout_margin="8dp"
            android:background="@drawable/bg_menu_item"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="16dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_permission_management" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="权限管理"
                android:textSize="16sp" />
        </LinearLayout>

    </GridLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 