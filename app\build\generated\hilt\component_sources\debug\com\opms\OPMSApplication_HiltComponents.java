package com.opms;

import com.opms.di.RepositoryModule;
import com.opms.di.module.AppModule;
import com.opms.di.module.NetworkModule;
import com.opms.ui.audit.UserAuditFragment_GeneratedInjector;
import com.opms.ui.business.BusinessFragment_GeneratedInjector;
import com.opms.ui.business.ComponentEditActivity_GeneratedInjector;
import com.opms.ui.business.ComponentManagementActivity_GeneratedInjector;
import com.opms.ui.business.CustomerEditActivity_GeneratedInjector;
import com.opms.ui.business.CustomerManagementActivity_GeneratedInjector;
import com.opms.ui.business.OrderEntryActivity_GeneratedInjector;
import com.opms.ui.business.OrderManagementActivity_GeneratedInjector;
import com.opms.ui.business.ProductEditActivity_GeneratedInjector;
import com.opms.ui.business.ProductManagementActivity_GeneratedInjector;
import com.opms.ui.business.dialog.ComponentSelectionDialog_GeneratedInjector;
import com.opms.ui.business.dialog.CustomerSelectionDialog_GeneratedInjector;
import com.opms.ui.business.dialog.ProductSelectionDialog_GeneratedInjector;
import com.opms.ui.business.dialog.UserSelectionDialog_GeneratedInjector;
import com.opms.ui.login.LoginActivity_GeneratedInjector;
import com.opms.ui.profile.AvatarViewActivity_GeneratedInjector;
import com.opms.ui.profile.PasswordChangeActivity_GeneratedInjector;
import com.opms.ui.profile.ProfileActivity_GeneratedInjector;
import com.opms.ui.profile.ProfileEditActivity_GeneratedInjector;
import com.opms.ui.profile.ProfileFragment_GeneratedInjector;
import com.opms.ui.register.RegisterActivity_GeneratedInjector;
import com.opms.ui.splash.SplashActivity_GeneratedInjector;
import com.opms.ui.system.DepartmentManagementActivity_GeneratedInjector;
import com.opms.ui.system.PermissionEditActivity_GeneratedInjector;
import com.opms.ui.system.PermissionManagementActivity_GeneratedInjector;
import com.opms.ui.system.PositionEditActivity_GeneratedInjector;
import com.opms.ui.system.PositionManagementActivity_GeneratedInjector;
import com.opms.ui.system.PostEditActivity_GeneratedInjector;
import com.opms.ui.system.PostManagementActivity_GeneratedInjector;
import com.opms.ui.system.ProcessPostMappingEditActivity_GeneratedInjector;
import com.opms.ui.system.ProcessPostMappingManagementActivity_GeneratedInjector;
import com.opms.ui.system.ProcessTemplateEditActivity_GeneratedInjector;
import com.opms.ui.system.ProcessTemplateManagementActivity_GeneratedInjector;
import com.opms.ui.system.SystemManageFragment_GeneratedInjector;
import com.opms.ui.system.UserAuditActivity_GeneratedInjector;
import com.opms.ui.system.UserAuditListActivity_GeneratedInjector;
import com.opms.ui.system.UserEditActivity_GeneratedInjector;
import com.opms.ui.system.UserManagementActivity_GeneratedInjector;
import com.opms.ui.todo.TodoFragment_GeneratedInjector;
import dagger.Binds;
import dagger.Component;
import dagger.Module;
import dagger.Subcomponent;
import dagger.hilt.android.components.ActivityComponent;
import dagger.hilt.android.components.ActivityRetainedComponent;
import dagger.hilt.android.components.FragmentComponent;
import dagger.hilt.android.components.ServiceComponent;
import dagger.hilt.android.components.ViewComponent;
import dagger.hilt.android.components.ViewModelComponent;
import dagger.hilt.android.components.ViewWithFragmentComponent;
import dagger.hilt.android.flags.FragmentGetContextFix;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.HiltViewModelFactory;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_DefaultViewModelFactories_ActivityModule;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import dagger.hilt.android.internal.lifecycle.HiltWrapper_HiltViewModelFactory_ViewModelModule;
import dagger.hilt.android.internal.managers.ActivityComponentManager;
import dagger.hilt.android.internal.managers.FragmentComponentManager;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import dagger.hilt.android.internal.managers.HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import dagger.hilt.android.internal.managers.ServiceComponentManager;
import dagger.hilt.android.internal.managers.ViewComponentManager;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.HiltWrapper_ActivityModule;
import dagger.hilt.android.scopes.ActivityRetainedScoped;
import dagger.hilt.android.scopes.ActivityScoped;
import dagger.hilt.android.scopes.FragmentScoped;
import dagger.hilt.android.scopes.ServiceScoped;
import dagger.hilt.android.scopes.ViewModelScoped;
import dagger.hilt.android.scopes.ViewScoped;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedComponent;
import dagger.hilt.migration.DisableInstallInCheck;
import javax.annotation.processing.Generated;
import javax.inject.Singleton;

@Generated("dagger.hilt.processor.internal.root.RootProcessor")
public final class OPMSApplication_HiltComponents {
  private OPMSApplication_HiltComponents() {
  }

  @Module(
      subcomponents = ServiceC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ServiceCBuilderModule {
    @Binds
    ServiceComponentBuilder bind(ServiceC.Builder builder);
  }

  @Module(
      subcomponents = ActivityRetainedC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ActivityRetainedCBuilderModule {
    @Binds
    ActivityRetainedComponentBuilder bind(ActivityRetainedC.Builder builder);
  }

  @Module(
      subcomponents = ActivityC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ActivityCBuilderModule {
    @Binds
    ActivityComponentBuilder bind(ActivityC.Builder builder);
  }

  @Module(
      subcomponents = ViewModelC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewModelCBuilderModule {
    @Binds
    ViewModelComponentBuilder bind(ViewModelC.Builder builder);
  }

  @Module(
      subcomponents = ViewC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewCBuilderModule {
    @Binds
    ViewComponentBuilder bind(ViewC.Builder builder);
  }

  @Module(
      subcomponents = FragmentC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface FragmentCBuilderModule {
    @Binds
    FragmentComponentBuilder bind(FragmentC.Builder builder);
  }

  @Module(
      subcomponents = ViewWithFragmentC.class
  )
  @DisableInstallInCheck
  @Generated("dagger.hilt.processor.internal.root.RootProcessor")
  abstract interface ViewWithFragmentCBuilderModule {
    @Binds
    ViewWithFragmentComponentBuilder bind(ViewWithFragmentC.Builder builder);
  }

  @Component(
      modules = {
          AppModule.class,
          ApplicationContextModule.class,
          HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
          NetworkModule.class,
          ActivityRetainedCBuilderModule.class,
          ServiceCBuilderModule.class,
          RepositoryModule.class,
          com.opms.di.module.RepositoryModule.class
      }
  )
  @Singleton
  public abstract static class SingletonC implements OPMSApplication_GeneratedInjector,
      FragmentGetContextFix.FragmentGetContextFixEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint,
      ServiceComponentManager.ServiceComponentBuilderEntryPoint,
      SingletonComponent,
      GeneratedComponent {
  }

  @Subcomponent
  @ServiceScoped
  public abstract static class ServiceC implements ServiceComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ServiceComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
          ActivityCBuilderModule.class,
          ViewModelCBuilderModule.class
      }
  )
  @ActivityRetainedScoped
  public abstract static class ActivityRetainedC implements ActivityRetainedComponent,
      ActivityComponentManager.ActivityComponentBuilderEntryPoint,
      HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityRetainedComponentBuilder {
    }
  }

  @Subcomponent(
      modules = {
          HiltWrapper_ActivityModule.class,
          HiltWrapper_DefaultViewModelFactories_ActivityModule.class,
          FragmentCBuilderModule.class,
          ViewCBuilderModule.class
      }
  )
  @ActivityScoped
  public abstract static class ActivityC implements MainActivity_GeneratedInjector,
      ComponentEditActivity_GeneratedInjector,
      ComponentManagementActivity_GeneratedInjector,
      CustomerEditActivity_GeneratedInjector,
      CustomerManagementActivity_GeneratedInjector,
      OrderEntryActivity_GeneratedInjector,
      OrderManagementActivity_GeneratedInjector,
      ProductEditActivity_GeneratedInjector,
      ProductManagementActivity_GeneratedInjector,
      LoginActivity_GeneratedInjector,
      AvatarViewActivity_GeneratedInjector,
      PasswordChangeActivity_GeneratedInjector,
      ProfileActivity_GeneratedInjector,
      ProfileEditActivity_GeneratedInjector,
      RegisterActivity_GeneratedInjector,
      SplashActivity_GeneratedInjector,
      DepartmentManagementActivity_GeneratedInjector,
      PermissionEditActivity_GeneratedInjector,
      PermissionManagementActivity_GeneratedInjector,
      PositionEditActivity_GeneratedInjector,
      PositionManagementActivity_GeneratedInjector,
      PostEditActivity_GeneratedInjector,
      PostManagementActivity_GeneratedInjector,
      ProcessPostMappingEditActivity_GeneratedInjector,
      ProcessPostMappingManagementActivity_GeneratedInjector,
      ProcessTemplateEditActivity_GeneratedInjector,
      ProcessTemplateManagementActivity_GeneratedInjector,
      UserAuditActivity_GeneratedInjector,
      UserAuditListActivity_GeneratedInjector,
      UserEditActivity_GeneratedInjector,
      UserManagementActivity_GeneratedInjector,
      ActivityComponent,
      DefaultViewModelFactories.ActivityEntryPoint,
      HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint,
      FragmentComponentManager.FragmentComponentBuilderEntryPoint,
      ViewComponentManager.ViewComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ActivityComponentBuilder {
    }
  }

  @Subcomponent(
      modules = HiltWrapper_HiltViewModelFactory_ViewModelModule.class
  )
  @ViewModelScoped
  public abstract static class ViewModelC implements ViewModelComponent,
      HiltViewModelFactory.ViewModelFactoriesEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewModelComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewC implements ViewComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewComponentBuilder {
    }
  }

  @Subcomponent(
      modules = ViewWithFragmentCBuilderModule.class
  )
  @FragmentScoped
  public abstract static class FragmentC implements UserAuditFragment_GeneratedInjector,
      BusinessFragment_GeneratedInjector,
      ComponentSelectionDialog_GeneratedInjector,
      CustomerSelectionDialog_GeneratedInjector,
      ProductSelectionDialog_GeneratedInjector,
      UserSelectionDialog_GeneratedInjector,
      ProfileFragment_GeneratedInjector,
      SystemManageFragment_GeneratedInjector,
      TodoFragment_GeneratedInjector,
      com.opms.ui.user.UserAuditFragment_GeneratedInjector,
      FragmentComponent,
      DefaultViewModelFactories.FragmentEntryPoint,
      ViewComponentManager.ViewWithFragmentComponentBuilderEntryPoint,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends FragmentComponentBuilder {
    }
  }

  @Subcomponent
  @ViewScoped
  public abstract static class ViewWithFragmentC implements ViewWithFragmentComponent,
      GeneratedComponent {
    @Subcomponent.Builder
    abstract interface Builder extends ViewWithFragmentComponentBuilder {
    }
  }
}
