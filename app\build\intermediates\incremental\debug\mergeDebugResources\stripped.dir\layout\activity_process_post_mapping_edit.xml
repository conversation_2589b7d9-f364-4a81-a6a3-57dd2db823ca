<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.ProcessPostMappingEditActivity">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/ThemeOverlay.Material3.Light"
        app:title="流程岗位映射" />

    <!-- 表单内容 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toTopOf="@id/ll_buttons"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- 流程选择 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="选择流程"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <Spinner
                android:id="@+id/spinner_process"
                android:layout_width="match_parent"
                android:layout_height="72dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/bg_spinner_with_arrow"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingTop="20dp"
                android:paddingEnd="48dp"
                android:paddingBottom="20dp" />

            <!-- 岗位选择 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="选择岗位"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <Spinner
                android:id="@+id/spinner_post"
                android:layout_width="match_parent"
                android:layout_height="72dp"
                android:layout_marginBottom="24dp"
                android:background="@drawable/bg_spinner_with_arrow"
                android:gravity="center_vertical"
                android:paddingStart="16dp"
                android:paddingTop="20dp"
                android:paddingEnd="48dp"
                android:paddingBottom="20dp" />

            <!-- 备注信息 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="备注信息"
                app:boxStrokeColor="@color/primary"
                app:hintTextColor="@color/primary">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_remark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textMultiLine"
                    android:lines="3"
                    android:maxLength="200"
                    android:scrollbars="vertical" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 状态设置 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="启用状态"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="启用后建立映射关系"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                    </LinearLayout>

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

        </LinearLayout>

    </ScrollView>

    <!-- 操作按钮 -->
    <LinearLayout
        android:id="@+id/ll_buttons"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/surface"
        android:elevation="8dp"
        android:orientation="horizontal"
        android:padding="16dp"
        android:weightSum="2"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_cancel"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="取消" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:text="保存" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
