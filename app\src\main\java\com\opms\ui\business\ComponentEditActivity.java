package com.opms.ui.business;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.snackbar.Snackbar;
import com.google.android.material.textfield.TextInputLayout;
import com.opms.R;
import com.opms.common.enums.BusinessImgType;
import com.opms.common.utils.ImageUploadUtils;
import com.opms.common.utils.MultiImageManager;
import com.opms.data.model.request.ComponentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ComponentResponse;
import com.opms.data.repository.ComponentRepository;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.databinding.ActivityComponentEditBinding;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class ComponentEditActivity extends AppCompatActivity {

    private static final String TAG = "ComponentEditActivity";

    @Inject
    ComponentRepository componentRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    private ActivityComponentEditBinding binding;
    private int componentId = -1;
    private boolean isEditMode = false;
    private MultiImageManager imageManager;
    private ActivityResultLauncher<Intent> imagePickerLauncher;
    private ActivityResultLauncher<String> permissionLauncher;

    // 编码校验相关
    private boolean isCodeValid = false;
    private boolean isCodeChecking = false;
    private Handler codeCheckHandler;
    private Runnable codeCheckRunnable;
    private TextInputLayout tilComponentCode;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityComponentEditBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        getIntentData();
        setupImagePicker();
        setupToolbar();
        setupCodeValidation();
        setupButtons();
        setupImageManager();

        if (isEditMode) {
            loadComponentDetail();
        }
    }

    private void getIntentData() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("component_id")) {
            componentId = intent.getIntExtra("component_id", -1);
            isEditMode = componentId != -1;
        }
    }

    private void setupImagePicker() {
        Log.d(TAG, "setupImagePicker: 初始化多图片选择器");

        // 权限请求launcher
        permissionLauncher = registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                isGranted -> {
                    Log.d(TAG, "权限请求结果: " + isGranted);
                    if (isGranted) {
                        openImagePicker();
                    } else {
                        showError("需要存储权限才能选择图片");
                    }
                }
        );

        // 多图片选择launcher
        imagePickerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    Log.d(TAG, "图片选择器返回结果: " + result.getResultCode());
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        java.util.List<Uri> imageUris = new java.util.ArrayList<>();

                        if (result.getData().getClipData() != null) {
                            // 多选模式
                            int count = result.getData().getClipData().getItemCount();
                            Log.d(TAG, "选择了 " + count + " 张图片");
                            for (int i = 0; i < count; i++) {
                                Uri imageUri = result.getData().getClipData().getItemAt(i).getUri();
                                if (isValidImageUri(imageUri)) {
                                    imageUris.add(imageUri);
                                }
                            }
                        } else if (result.getData().getData() != null) {
                            // 单选模式
                            Uri imageUri = result.getData().getData();
                            Log.d(TAG, "选择了 1 张图片: " + imageUri);
                            if (isValidImageUri(imageUri)) {
                                imageUris.add(imageUri);
                            }
                        }

                        if (!imageUris.isEmpty()) {
                            Log.d(TAG, "选中 " + imageUris.size() + " 张图片");
                            imageManager.addImages(imageUris);
                        } else {
                            Log.w(TAG, "未选择有效的图片");
                            showError("未选择有效的图片");
                        }
                    } else {
                        Log.w(TAG, "图片选择被取消或失败");
                    }
                }
        );
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle(isEditMode ? "编辑部件" : "新增部件");
        }
    }

    private void setupCodeValidation() {
        // 初始化Handler
        codeCheckHandler = new Handler(Looper.getMainLooper());

        // 获取TextInputLayout引用
        tilComponentCode = findViewById(R.id.til_component_code);
        if (tilComponentCode == null) {
            // 如果没有找到TextInputLayout，创建一个临时的
            tilComponentCode = new TextInputLayout(this);
        }

        // 在编辑模式下禁用编码字段并设置灰色背景
        if (isEditMode) {
            binding.etComponentCode.setEnabled(false);
            binding.etComponentCode.setBackgroundColor(Color.parseColor("#F5F5F5"));
            isCodeValid = true; // 编辑模式下认为编码有效
            return;
        }

        // 新增模式下设置编码校验
        binding.etComponentCode.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 取消之前的检查
                if (codeCheckRunnable != null) {
                    codeCheckHandler.removeCallbacks(codeCheckRunnable);
                }

                String code = s.toString().trim();

                // 清除之前的错误
                binding.etComponentCode.setError(null);
                isCodeValid = false;

                // 如果编码不为空，检查是否已存在
                if (!TextUtils.isEmpty(code)) {
                    isCodeChecking = true;
                    showCodeCheckingStatus(getString(R.string.checking_component_code));

                    // 延迟500ms后进行API调用，避免频繁请求
                    codeCheckRunnable = () -> checkComponentCodeExists(code);
                    codeCheckHandler.postDelayed(codeCheckRunnable, 500);
                }
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });
    }

    private void setupButtons() {
        binding.btnSave.setOnClickListener(v -> saveComponent());
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void setupImageManager() {
        Log.d(TAG, "setupImageManager: 设置多图片管理器，编辑模式: " + isEditMode);

        // 初始化多图片管理器
        imageManager = new MultiImageManager(this, binding.rvComponentImages);

        // 设置配置
        imageManager.setup(
                imageUploadRepository,
                BusinessImgType.COMPONENT,
                String.valueOf(componentId),
                getCurrentUser(),
                isEditMode
        );

        // 设置监听器
        imageManager.setOnImageActionListener(new MultiImageManager.OnImageActionListener() {
            @Override
            public void onAddImageClick() {
                Log.d(TAG, "点击添加图片按钮");
                checkPermissionAndOpenImagePicker();
            }

            @Override
            public void onImageClick(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "点击图片: " + position);
                showImagePreview(item);
            }

            @Override
            public void onImageLongClick(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "长按图片: " + position);
                showImageOptions(item, position);
            }

            @Override
            public void onImageDelete(MultiImageManager.ImageItem item, int position) {
                Log.d(TAG, "删除图片: " + position);
                confirmDeleteImage(item, position);
            }

            @Override
            public void onImageUploadProgress(int uploadedCount, int totalCount) {
                Log.d(TAG, "图片上传进度: " + uploadedCount + "/" + totalCount);
            }

            @Override
            public void onImageUploadComplete(java.util.List<String> successUrls, java.util.List<String> failureMessages) {
                Log.d(TAG, "图片上传完成 - 成功: " + successUrls.size() + ", 失败: " + failureMessages.size());
                if (!failureMessages.isEmpty()) {
                    showError("部分图片上传失败: " + failureMessages.get(0));
                } else if (!successUrls.isEmpty()) {
                    showMessage("图片上传成功");
                }
            }
        });
    }

    private void loadComponentDetail() {
        showLoading(true);

        Call<ApiResponse<ComponentResponse>> call = componentRepository.getComponentDetail(componentId);
        call.enqueue(new Callback<ApiResponse<ComponentResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ComponentResponse>> call,
                                   Response<ApiResponse<ComponentResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ComponentResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        ComponentResponse component = apiResponse.getData();
                        if (component != null) {
                            fillComponentData(component);
                        }
                    } else {
                        showError("获取部件详情失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ComponentResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private void checkComponentCodeExists(String code) {
        componentRepository.checkComponentCodeExists(code).enqueue(new Callback<ApiResponse<ComponentResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ComponentResponse>> call, Response<ApiResponse<ComponentResponse>> response) {
                isCodeChecking = false;
                hideCodeCheckingStatus();

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ComponentResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        // 编码已存在
                        binding.etComponentCode.setError(getString(R.string.error_component_code_exists));
                        isCodeValid = false;
                    } else {
                        // 编码不存在，可以使用
                        binding.etComponentCode.setError(null);
                        showCodeCheckingStatus(getString(R.string.component_code_available));
                        isCodeValid = true;
                    }
                } else {
                    // API错误，假设编码可用
                    binding.etComponentCode.setError(null);
                    showCodeCheckingStatus(getString(R.string.component_code_available));
                    isCodeValid = true;
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ComponentResponse>> call, Throwable t) {
                isCodeChecking = false;
                hideCodeCheckingStatus();
                // 网络错误，假设编码可用
                binding.etComponentCode.setError(null);
                isCodeValid = true;
                Log.e(TAG, "Component code check failed: " + t.getMessage(), t);
            }
        });
    }

    private void showCodeCheckingStatus(String message) {
        // 可以在这里显示检查状态，比如在TextInputLayout的helper text中
        if (tilComponentCode != null) {
            tilComponentCode.setHelperText(message);
        }
    }

    private void hideCodeCheckingStatus() {
        if (tilComponentCode != null) {
            tilComponentCode.setHelperText(null);
        }
    }

    private void fillComponentData(ComponentResponse component) {
        binding.etComponentName.setText(component.getName());
        binding.etComponentCode.setText(component.getCode());
        binding.etComponentModel.setText(component.getModel());
        binding.etComponentStandard.setText(component.getStandard());
        binding.etComponentRemark.setText(component.getRemark());

        // 加载部件图片列表
        if (imageManager != null) {
            imageManager.loadImages();
        }
    }

    private void saveComponent() {
        if (!validateInput()) {
            return;
        }

        ComponentRequest request = createComponentRequest();
        showLoading(true);

        Call<ApiResponse<ComponentResponse>> call;
        if (isEditMode) {
            call = componentRepository.updateComponent(componentId, request);
        } else {
            call = componentRepository.createComponent(request);
        }

        call.enqueue(new Callback<ApiResponse<ComponentResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<ComponentResponse>> call,
                                   Response<ApiResponse<ComponentResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<ComponentResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        String message = isEditMode ? "部件更新成功" : "部件创建成功";
                        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();

                        // 如果是新增模式且有上传的图片，需要更新图片的业务ID关联
                        if (!isEditMode && apiResponse.getData() != null) {
                            ComponentResponse newComponent = apiResponse.getData();
                            if (newComponent.getId() > 0) {
                                Log.d(TAG, "新增部件成功，ID: " + newComponent.getId() + "，更新图片业务ID关联");
                                imageManager.updateBusinessId(String.valueOf(newComponent.getId()));
                            }
                        }

                        // 返回结果
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError("保存失败: " + apiResponse.getMessage());
                    }
                } else {
                    showError("网络请求失败，请检查网络连接");
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<ComponentResponse>> call, Throwable t) {
                showLoading(false);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        String name = binding.etComponentName.getText().toString().trim();
        String code = binding.etComponentCode.getText().toString().trim();

        if (TextUtils.isEmpty(name)) {
            binding.etComponentName.setError("部件名称不能为空");
            binding.etComponentName.requestFocus();
            return false;
        }

        if (TextUtils.isEmpty(code)) {
            binding.etComponentCode.setError(getString(R.string.error_component_code_empty));
            binding.etComponentCode.requestFocus();
            return false;
        }

        // 新增模式下检查编码是否有效
        if (!isEditMode && !isCodeValid) {
            if (isCodeChecking) {
                showError("正在检查部件编码，请稍候...");
            } else {
                binding.etComponentCode.setError(getString(R.string.error_component_code_exists));
                binding.etComponentCode.requestFocus();
            }
            return false;
        }

        return true;
    }

    private ComponentRequest createComponentRequest() {
        ComponentRequest request = new ComponentRequest();
        request.setId(componentId);
        request.setName(binding.etComponentName.getText().toString().trim());
        request.setCode(binding.etComponentCode.getText().toString().trim());
        request.setModel(binding.etComponentModel.getText().toString().trim());
        request.setStandard(binding.etComponentStandard.getText().toString().trim());
        request.setRemark(binding.etComponentRemark.getText().toString().trim());

        // 新增时不设置image字段，编辑时也不在这里设置（单独上传）
        // request.setImage(null);

        return request;
    }

    private void checkPermissionAndOpenImagePicker() {
        Log.d(TAG, "checkPermissionAndOpenImagePicker: 检查权限并打开图片选择器");

        // 检查权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ 使用READ_MEDIA_IMAGES权限
            if (checkSelfPermission(Manifest.permission.READ_MEDIA_IMAGES) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_MEDIA_IMAGES权限");
                permissionLauncher.launch(Manifest.permission.READ_MEDIA_IMAGES);
                return;
            }
        } else {
            // Android 12及以下使用READ_EXTERNAL_STORAGE权限
            if (checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "请求READ_EXTERNAL_STORAGE权限");
                permissionLauncher.launch(Manifest.permission.READ_EXTERNAL_STORAGE);
                return;
            }
        }

        openImagePicker();
    }

    private void openImagePicker() {
        Log.d(TAG, "openImagePicker: 启动多图片选择器");
        try {
            Intent intent = new Intent(Intent.ACTION_GET_CONTENT);
            intent.setType("image/*");
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true);
            intent.addCategory(Intent.CATEGORY_OPENABLE);

            Intent chooser = Intent.createChooser(intent, "选择图片");
            imagePickerLauncher.launch(chooser);
        } catch (Exception e) {
            Log.e(TAG, "openImagePicker: 启动图片选择器失败", e);
            showError("无法打开图片选择器: " + e.getMessage());
        }
    }

    /**
     * 验证图片URI是否有效
     */
    private boolean isValidImageUri(Uri uri) {
        try {
            String mimeType = getContentResolver().getType(uri);
            return mimeType != null && mimeType.startsWith("image/");
        } catch (Exception e) {
            Log.e(TAG, "验证图片URI失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 显示图片预览
     */
    private void showImagePreview(MultiImageManager.ImageItem item) {
        Log.d(TAG, "显示图片预览");

        try {
            if (item.getImageUrl() != null) {
                // 预览服务器图片
                Log.d(TAG, "预览服务器图片: " + item.getImageUrl());
                com.opms.ui.common.ImagePreviewActivity.startWithUrl(this, item.getImageUrl(), "部件图片");
            } else if (item.getImageUri() != null) {
                // 预览本地图片
                Log.d(TAG, "预览本地图片: " + item.getImageUri());
                com.opms.ui.common.ImagePreviewActivity.startWithUri(this, item.getImageUri(), "部件图片");
            } else {
                Log.w(TAG, "图片项没有有效的URL或URI");
                showError("无法预览图片：图片源无效");
            }
        } catch (Exception e) {
            Log.e(TAG, "启动图片预览失败", e);
            showError("启动图片预览失败: " + e.getMessage());
        }
    }

    /**
     * 显示图片操作选项
     */
    private void showImageOptions(MultiImageManager.ImageItem item, int position) {
        String[] options = {"预览", "删除"};

        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("图片操作")
                .setItems(options, (dialog, which) -> {
                    switch (which) {
                        case 0: // 预览
                            showImagePreview(item);
                            break;
                        case 1: // 删除
                            confirmDeleteImage(item, position);
                            break;
                    }
                })
                .show();
    }

    /**
     * 确认删除图片
     */
    private void confirmDeleteImage(MultiImageManager.ImageItem item, int position) {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("删除图片")
                .setMessage("确定要删除这张图片吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    imageManager.deleteImage(item, position);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 获取当前用户
     */
    private String getCurrentUser() {
        return ImageUploadUtils.getCurrentUser(this);
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnSave.setEnabled(!show);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private void showMessage(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_SHORT).show();
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }
}
