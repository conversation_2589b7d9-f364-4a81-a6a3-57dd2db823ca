<?xml version="1.0" encoding="utf-8"?><!-- 用户分配信息卡片（仅通过时显示） -->
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_user_assignment"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    android:visibility="gone"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 卡片标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="用户分配信息"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold" />

        <!-- 工号 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_employee_number"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="工号">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_employee_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 角色类型 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_role_type"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="角色类型">

            <AutoCompleteTextView
                android:id="@+id/act_role_type"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 部门 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_department"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="部门">

            <AutoCompleteTextView
                android:id="@+id/act_department"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 职务 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_position"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="职务">

            <AutoCompleteTextView
                android:id="@+id/act_position"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 岗位 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_post"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:hint="岗位">

            <AutoCompleteTextView
                android:id="@+id/act_post"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 权限模板 -->
        <com.google.android.material.textfield.TextInputLayout
            android:id="@+id/til_permission_template"
            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="权限模板">

            <AutoCompleteTextView
                android:id="@+id/act_permission_template"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
