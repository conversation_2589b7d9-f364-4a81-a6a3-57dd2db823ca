<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="@dimen/spacing_large">

    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="@dimen/image_size_xlarge"
        android:layout_height="@dimen/image_size_xlarge"
        android:layout_marginTop="@dimen/spacing_xlarge"
        android:src="@mipmap/ic_launcher"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/spacing_medium"
        android:text="@string/app_name"
        android:textColor="@color/text_primary"
        android:textSize="@dimen/text_size_xxlarge"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_logo" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_username"
        style="@style/Widget.OPMS.TextInputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/spacing_xlarge"
        android:hint="@string/username"
        app:layout_constraintTop_toBottomOf="@id/tv_title">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_username"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="text"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/til_password"
        style="@style/Widget.OPMS.TextInputLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/spacing_medium"
        android:hint="@string/password"
        app:layout_constraintTop_toBottomOf="@id/til_username"
        app:passwordToggleEnabled="true">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/et_password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLines="1" />

    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_login"
        style="@style/Widget.OPMS.Button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height"
        android:layout_marginTop="@dimen/spacing_large"
        android:text="@string/login"
        app:layout_constraintTop_toBottomOf="@id/til_password" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/btn_register"
        style="@style/Widget.MaterialComponents.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/spacing_medium"
        android:text="@string/register"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/btn_login" />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout> 