<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp">

    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_image"
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:clickable="true"
        android:focusable="true"
        android:foreground="?android:attr/selectableItemBackground"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_image_placeholder" />

        <!-- 上传中覆盖层 -->
        <View
            android:id="@+id/view_uploading_overlay"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black_50"
            android:visibility="gone" />

        <!-- 上传中进度指示器 -->
        <ProgressBar
            android:id="@+id/progress_uploading"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:indeterminateTint="@color/white"
            android:visibility="gone" />

    </com.google.android.material.card.MaterialCardView>

    <!-- 删除按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/btn_delete"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="top|end"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="4dp"
        android:src="@drawable/ic_close"
        android:visibility="gone"
        app:backgroundTint="@color/red_500"
        app:fabCustomSize="24dp"
        app:maxImageSize="12dp"
        app:tint="@color/white" />

</FrameLayout>
