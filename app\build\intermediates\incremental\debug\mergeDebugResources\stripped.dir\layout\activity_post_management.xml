<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.PostManagementActivity">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="岗位管理"
        app:titleTextColor="@color/white" />

    <!-- 紧凑工具栏 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_compact_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_marginTop="8dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="1dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp"
            android:paddingVertical="12dp">

            <!-- 搜索按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_search"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:text="搜索"
                app:icon="@drawable/ic_search"
                app:iconGravity="textStart"
                app:iconSize="18dp" />

            <!-- 分隔线 -->
            <View
                android:layout_width="1dp"
                android:layout_height="20dp"
                android:layout_marginEnd="12dp"
                android:background="@color/divider" />

            <!-- 统计信息 -->
            <TextView
                android:id="@+id/tv_post_count"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="共 0 个岗位"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 展开工具栏按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_toggle_toolbar"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="0dp"
                android:text="工具"
                app:icon="@drawable/ic_expand_more"
                app:iconGravity="textEnd"
                app:iconSize="18dp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 可折叠的搜索工具栏 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_expandable_tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="8dp"
        android:layout_marginTop="4dp"
        android:visibility="gone"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/card_compact_toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 搜索框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_search"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="搜索岗位名称或代码"
                app:endIconDrawable="@drawable/ic_clear"
                app:endIconMode="clear_text"
                app:startIconDrawable="@drawable/ic_search">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 岗位列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_expandable_tools">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_posts"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            android:paddingEnd="8dp"
            android:paddingBottom="80dp"
            tools:listitem="@layout/item_post" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 空状态视图 -->
    <LinearLayout
        android:id="@+id/ll_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:alpha="0.5"
            android:src="@drawable/ic_assignment"
            app:tint="@color/text_secondary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="暂无岗位数据"
            android:textColor="@color/text_secondary"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="点击右下角按钮添加岗位"
            android:textColor="@color/text_hint"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 浮动操作按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:contentDescription="添加岗位"
        android:src="@drawable/ic_add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@color/white" />

</androidx.constraintlayout.widget.ConstraintLayout>
