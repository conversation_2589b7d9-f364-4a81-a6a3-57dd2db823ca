package com.opms;

import dagger.hilt.internal.aggregatedroot.codegen._com_opms_OPMSApplication;
import dagger.hilt.internal.componenttreedeps.ComponentTreeDeps;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ActivityRetainedComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_FragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ServiceComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewModelComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_components_ViewWithFragmentComponent;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_FragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ServiceComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewModelComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder;
import dagger.hilt.processor.internal.definecomponent.codegen._dagger_hilt_components_SingletonComponent;
import hilt_aggregated_deps._com_opms_MainActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_OPMSApplication_GeneratedInjector;
import hilt_aggregated_deps._com_opms_di_RepositoryModule;
import hilt_aggregated_deps._com_opms_di_module_AppModule;
import hilt_aggregated_deps._com_opms_di_module_NetworkModule;
import hilt_aggregated_deps._com_opms_di_module_RepositoryModule;
import hilt_aggregated_deps._com_opms_ui_audit_UserAuditFragment_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_BusinessFragment_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_ComponentEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_ComponentManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_CustomerEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_CustomerManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_OrderEntryActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_OrderManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_ProductEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_ProductManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_dialog_ComponentSelectionDialog_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_dialog_CustomerSelectionDialog_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_dialog_ProductSelectionDialog_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_business_dialog_UserSelectionDialog_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_login_LoginActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_profile_AvatarViewActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_profile_PasswordChangeActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_profile_ProfileActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_profile_ProfileEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_profile_ProfileFragment_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_register_RegisterActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_splash_SplashActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_DepartmentManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_PermissionEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_PermissionManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_PositionEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_PositionManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_PostEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_PostManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_ProcessPostMappingEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_ProcessPostMappingManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_ProcessTemplateEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_ProcessTemplateManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_SystemManageFragment_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_UserAuditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_UserAuditListActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_UserEditActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_system_UserManagementActivity_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_todo_TodoFragment_GeneratedInjector;
import hilt_aggregated_deps._com_opms_ui_user_UserAuditFragment_GeneratedInjector;
import hilt_aggregated_deps._dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_ApplicationContextModule;
import hilt_aggregated_deps._dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule;

@ComponentTreeDeps(
    rootDeps = _com_opms_OPMSApplication.class,
    defineComponentDeps = {
        _dagger_hilt_android_components_ActivityComponent.class,
        _dagger_hilt_android_components_ActivityRetainedComponent.class,
        _dagger_hilt_android_components_FragmentComponent.class,
        _dagger_hilt_android_components_ServiceComponent.class,
        _dagger_hilt_android_components_ViewComponent.class,
        _dagger_hilt_android_components_ViewModelComponent.class,
        _dagger_hilt_android_components_ViewWithFragmentComponent.class,
        _dagger_hilt_android_internal_builders_ActivityComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ActivityRetainedComponentBuilder.class,
        _dagger_hilt_android_internal_builders_FragmentComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ServiceComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewModelComponentBuilder.class,
        _dagger_hilt_android_internal_builders_ViewWithFragmentComponentBuilder.class,
        _dagger_hilt_components_SingletonComponent.class
    },
    aggregatedDeps = {
        _com_opms_MainActivity_GeneratedInjector.class,
        _com_opms_OPMSApplication_GeneratedInjector.class,
        _com_opms_di_RepositoryModule.class,
        _com_opms_di_module_AppModule.class,
        _com_opms_di_module_NetworkModule.class,
        _com_opms_di_module_RepositoryModule.class,
        _com_opms_ui_audit_UserAuditFragment_GeneratedInjector.class,
        _com_opms_ui_business_BusinessFragment_GeneratedInjector.class,
        _com_opms_ui_business_ComponentEditActivity_GeneratedInjector.class,
        _com_opms_ui_business_ComponentManagementActivity_GeneratedInjector.class,
        _com_opms_ui_business_CustomerEditActivity_GeneratedInjector.class,
        _com_opms_ui_business_CustomerManagementActivity_GeneratedInjector.class,
        _com_opms_ui_business_OrderEntryActivity_GeneratedInjector.class,
        _com_opms_ui_business_OrderManagementActivity_GeneratedInjector.class,
        _com_opms_ui_business_ProductEditActivity_GeneratedInjector.class,
        _com_opms_ui_business_ProductManagementActivity_GeneratedInjector.class,
        _com_opms_ui_business_dialog_ComponentSelectionDialog_GeneratedInjector.class,
        _com_opms_ui_business_dialog_CustomerSelectionDialog_GeneratedInjector.class,
        _com_opms_ui_business_dialog_ProductSelectionDialog_GeneratedInjector.class,
        _com_opms_ui_business_dialog_UserSelectionDialog_GeneratedInjector.class,
        _com_opms_ui_login_LoginActivity_GeneratedInjector.class,
        _com_opms_ui_profile_AvatarViewActivity_GeneratedInjector.class,
        _com_opms_ui_profile_PasswordChangeActivity_GeneratedInjector.class,
        _com_opms_ui_profile_ProfileActivity_GeneratedInjector.class,
        _com_opms_ui_profile_ProfileEditActivity_GeneratedInjector.class,
        _com_opms_ui_profile_ProfileFragment_GeneratedInjector.class,
        _com_opms_ui_register_RegisterActivity_GeneratedInjector.class,
        _com_opms_ui_splash_SplashActivity_GeneratedInjector.class,
        _com_opms_ui_system_DepartmentManagementActivity_GeneratedInjector.class,
        _com_opms_ui_system_PermissionEditActivity_GeneratedInjector.class,
        _com_opms_ui_system_PermissionManagementActivity_GeneratedInjector.class,
        _com_opms_ui_system_PositionEditActivity_GeneratedInjector.class,
        _com_opms_ui_system_PositionManagementActivity_GeneratedInjector.class,
        _com_opms_ui_system_PostEditActivity_GeneratedInjector.class,
        _com_opms_ui_system_PostManagementActivity_GeneratedInjector.class,
        _com_opms_ui_system_ProcessPostMappingEditActivity_GeneratedInjector.class,
        _com_opms_ui_system_ProcessPostMappingManagementActivity_GeneratedInjector.class,
        _com_opms_ui_system_ProcessTemplateEditActivity_GeneratedInjector.class,
        _com_opms_ui_system_ProcessTemplateManagementActivity_GeneratedInjector.class,
        _com_opms_ui_system_SystemManageFragment_GeneratedInjector.class,
        _com_opms_ui_system_UserAuditActivity_GeneratedInjector.class,
        _com_opms_ui_system_UserAuditListActivity_GeneratedInjector.class,
        _com_opms_ui_system_UserEditActivity_GeneratedInjector.class,
        _com_opms_ui_system_UserManagementActivity_GeneratedInjector.class,
        _com_opms_ui_todo_TodoFragment_GeneratedInjector.class,
        _com_opms_ui_user_UserAuditFragment_GeneratedInjector.class,
        _dagger_hilt_android_flags_FragmentGetContextFix_FragmentGetContextFixEntryPoint.class,
        _dagger_hilt_android_flags_HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_ActivityEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_DefaultViewModelFactories_FragmentEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltViewModelFactory_ViewModelFactoriesEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_DefaultViewModelFactories_ActivityModule.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ActivityCreatorEntryPoint.class,
        _dagger_hilt_android_internal_lifecycle_HiltWrapper_HiltViewModelFactory_ViewModelModule.class,
        _dagger_hilt_android_internal_managers_ActivityComponentManager_ActivityComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_FragmentComponentManager_FragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_ActivityRetainedLifecycleEntryPoint.class,
        _dagger_hilt_android_internal_managers_HiltWrapper_ActivityRetainedComponentManager_LifecycleModule.class,
        _dagger_hilt_android_internal_managers_ServiceComponentManager_ServiceComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_managers_ViewComponentManager_ViewWithFragmentComponentBuilderEntryPoint.class,
        _dagger_hilt_android_internal_modules_ApplicationContextModule.class,
        _dagger_hilt_android_internal_modules_HiltWrapper_ActivityModule.class
    }
)
public final class OPMSApplication_ComponentTreeDeps {
}
