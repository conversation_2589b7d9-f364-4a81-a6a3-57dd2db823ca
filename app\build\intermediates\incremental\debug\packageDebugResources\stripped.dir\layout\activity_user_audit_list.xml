<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.UserAuditListActivity">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="用户审核"
        app:titleTextColor="@color/white" />

    <!-- 搜索工具栏 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_search"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 搜索框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/til_search"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="搜索用户名、姓名或手机号"
                app:endIconDrawable="@drawable/ic_search"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 状态筛选 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:orientation="horizontal">

                <com.google.android.material.chip.ChipGroup
                    android:id="@+id/chip_group_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:checkedChip="@id/chip_pending"
                    app:selectionRequired="true"
                    app:singleSelection="true">

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_pending"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="待审核" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_approved"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="已通过" />

                    <com.google.android.material.chip.Chip
                        android:id="@+id/chip_rejected"
                        style="@style/Widget.MaterialComponents.Chip.Filter"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="已拒绝" />

                </com.google.android.material.chip.ChipGroup>

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 用户列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_search">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_users"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp"
            tools:itemCount="5"
            tools:listitem="@layout/item_pending_user" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/layout_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_search">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:alpha="0.5"
            android:src="@drawable/ic_person"
            app:tint="@color/text_secondary" />

        <TextView
            android:id="@+id/tv_empty_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="暂无相关用户"
            android:textColor="@color/text_secondary"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
