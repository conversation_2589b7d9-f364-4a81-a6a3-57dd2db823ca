package com.opms.common.utils;

import android.content.Context;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;
import com.google.android.material.card.MaterialCardView;
import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.enums.BusinessImgType;
import com.opms.data.repository.ImageUploadRepository;

import java.util.ArrayList;
import java.util.List;

/**
 * 多图片管理工具类
 * 提供多图片显示、上传、删除的UI管理功能
 */
public class MultiImageManager {

    private static final String TAG = "MultiImageManager";
    private static final int MAX_IMAGES = 9; // 最大图片数量

    private Context context;
    private RecyclerView recyclerView;
    private ImageAdapter adapter;
    private List<ImageItem> imageItems;
    private ImageUploadRepository imageUploadRepository;
    private BusinessImgType businessType;
    private String businessId;
    private String operator;
    private boolean isEditMode;
    private OnImageActionListener listener;
    private String tempBusinessId; // 新增模式下的临时业务ID

    /**
     * 构造函数
     */
    public MultiImageManager(Context context, RecyclerView recyclerView) {
        this.context = context;
        this.recyclerView = recyclerView;
        this.imageItems = new ArrayList<>();
        initRecyclerView();
    }

    /**
     * 初始化RecyclerView
     */
    private void initRecyclerView() {
        adapter = new ImageAdapter();
        recyclerView.setLayoutManager(new GridLayoutManager(context, 3));
        recyclerView.setAdapter(adapter);
    }

    /**
     * 设置配置
     */
    public void setup(ImageUploadRepository imageUploadRepository,
                      BusinessImgType businessType,
                      String businessId,
                      String operator,
                      boolean isEditMode) {
        this.imageUploadRepository = imageUploadRepository;
        this.businessType = businessType;
        this.businessId = businessId;
        this.operator = operator;
        this.isEditMode = isEditMode;

        // 在新增模式下生成临时业务ID
        if (!isEditMode) {
            this.tempBusinessId = "temp_" + System.currentTimeMillis();
            Log.d(TAG, "新增模式，生成临时业务ID: " + tempBusinessId);
        }

        updateAddButton();
    }

    /**
     * 设置监听器
     */
    public void setOnImageActionListener(OnImageActionListener listener) {
        this.listener = listener;
    }

    /**
     * 更新业务ID（用于新增模式保存成功后）
     */
    public void updateBusinessId(String newBusinessId) {
        if (!isEditMode && tempBusinessId != null) {
            Log.d(TAG, "更新业务ID: " + tempBusinessId + " -> " + newBusinessId);

            // 调用API更新图片的业务ID关联
            if (imageUploadRepository != null) {
                imageUploadRepository.updateImageBusinessId(businessType, tempBusinessId, newBusinessId, operator,
                        new ImageUploadUtils.ImageUpdateCallback() {
                            @Override
                            public void onUpdateSuccess() {
                                Log.d(TAG, "图片业务ID更新成功");
                                // 更新本地状态
                                businessId = newBusinessId;
                                isEditMode = true;
                                tempBusinessId = null;
                            }

                            @Override
                            public void onUpdateFailure(String errorMessage) {
                                Log.e(TAG, "图片业务ID更新失败: " + errorMessage);
                            }
                        });
            }
        }
    }

    /**
     * 加载图片列表
     */
    public void loadImages() {
        if (imageUploadRepository == null) return;

        // 在新增模式下不加载图片，因为还没有实际的businessId
        if (!isEditMode) {
            Log.d(TAG, "新增模式下不加载图片列表");
            imageItems.clear();
            updateAddButton();
            adapter.notifyDataSetChanged();
            return;
        }

        imageUploadRepository.getImages(businessType, businessId, new ImageUploadUtils.ImageListCallback() {
            @Override
            public void onLoadStart() {
                Log.d(TAG, "开始加载图片列表");
            }

            @Override
            public void onLoadSuccess(List<ImageResponse> imageResponseList) {
                Log.d(TAG, "加载图片列表成功，数量: " + imageResponseList.size());
                imageItems.clear();
                for (ImageResponse response : imageResponseList) {
                    // 处理图片URL，确保包含完整的服务器地址
                    String processedUrl = processImageUrl(response.getFilePath());
                    String id = response.getId();
                    imageItems.add(new ImageItem(id, processedUrl));
                }
                updateAddButton();
                adapter.notifyDataSetChanged();
            }

            @Override
            public void onLoadFailure(String errorMessage) {
                Log.e(TAG, "加载图片列表失败: " + errorMessage);
                showMessage("加载图片失败: " + errorMessage);
            }

            @Override
            public void onLoadComplete() {
                Log.d(TAG, "图片列表加载完成");
            }
        });
    }

    /**
     * 添加新图片
     */
    public void addImages(List<Uri> imageUris) {
        if (imageUris == null || imageUris.isEmpty()) return;

        // 检查是否超过最大数量
        int currentImageCount = getCurrentImageCount();
        if (currentImageCount + imageUris.size() > MAX_IMAGES) {
            showMessage("最多只能添加 " + MAX_IMAGES + " 张图片");
            return;
        }

        // 添加到列表中（显示为待上传状态）
        List<ImageItem> newItems = new ArrayList<>();
        for (Uri uri : imageUris) {
            ImageItem item = new ImageItem(uri);
            item.setUploading(true);
            newItems.add(item);
        }

        // 移除添加按钮
        removeAddButton();

        // 添加新图片项
        imageItems.addAll(newItems);
        updateAddButton();
        adapter.notifyDataSetChanged();

        // 开始上传
        uploadImages(imageUris);
    }

    /**
     * 上传图片
     */
    private void uploadImages(List<Uri> imageUris) {
        if (imageUploadRepository == null) return;

        // 在新增模式下使用临时ID，编辑模式下使用实际ID
        String uploadBusinessId = isEditMode ? businessId : tempBusinessId;
        Log.d(TAG, "上传图片，使用业务ID: " + uploadBusinessId + "，编辑模式: " + isEditMode);

        imageUploadRepository.uploadMultipleImages(context, imageUris, businessType, uploadBusinessId, operator,
                new ImageUploadUtils.MultiImageUploadCallback() {
                    @Override
                    public void onUploadStart() {
                        Log.d(TAG, "开始批量上传图片");
                    }

                    @Override
                    public void onUploadProgress(int uploadedCount, int totalCount) {
                        if (listener != null) {
                            listener.onImageUploadProgress(uploadedCount, totalCount);
                        }
                    }

                    @Override
                    public void onSingleImageSuccess(String imageUrl) {
                        // 更新对应的图片项
                        updateUploadedImage(imageUrl);
                    }

                    @Override
                    public void onSingleImageFailure(String errorMessage) {
                        Log.e(TAG, "单个图片上传失败: " + errorMessage);
                    }

                    @Override
                    public void onUploadComplete(List<String> successUrls, List<String> failureMessages) {
                        Log.d(TAG, "批量上传完成，成功: " + successUrls.size() + ", 失败: " + failureMessages.size());

                        // 移除上传失败的项
                        removeFailedUploads();

                        // 在编辑模式下重新加载图片列表以确保显示最新状态
                        // 在新增模式下不需要重新加载，因为图片已经在本地显示
                        if (!successUrls.isEmpty() && isEditMode) {
                            Log.d(TAG, "编辑模式下有图片上传成功，重新加载图片列表");
                            loadImages();
                        }

                        if (listener != null) {
                            listener.onImageUploadComplete(successUrls, failureMessages);
                        }

                        if (!failureMessages.isEmpty()) {
                            showMessage("部分图片上传失败");
                        } else if (!successUrls.isEmpty()) {
                            showMessage("图片上传成功");
                        }
                    }
                });
    }

    /**
     * 删除图片
     */
    public void deleteImage(ImageItem item, int position) {
        if (item.getImageUrl() == null || item.getImageId() == null) {
            // 如果是本地图片或没有imageId，直接删除
            Log.d(TAG, "删除本地图片，位置: " + position);
            imageItems.remove(position);
            updateAddButton();
            adapter.notifyItemRemoved(position);
            return;
        }

        // 删除服务器图片
        if (imageUploadRepository != null) {
            String imageId = item.getImageId();
            Log.d(TAG, "准备删除服务器图片，ID: " + imageId);

            // 在新增模式下使用临时ID，编辑模式下使用实际ID
            String deleteBusinessId = isEditMode ? businessId : tempBusinessId;

            imageUploadRepository.deleteImage(businessType, deleteBusinessId, operator, item.imageId,
                    new ImageUploadUtils.ImageDeleteCallback() {
                        @Override
                        public void onDeleteStart() {
                            Log.d(TAG, "开始删除图片: " + item.imageId);
                        }

                        @Override
                        public void onDeleteSuccess() {
                            Log.d(TAG, "删除图片成功");
                            imageItems.remove(position);
                            updateAddButton();
                            adapter.notifyItemRemoved(position);
                            showMessage("图片删除成功");
                        }

                        @Override
                        public void onDeleteFailure(String errorMessage) {
                            Log.e(TAG, "删除图片失败: " + errorMessage);
                            showMessage("删除图片失败: " + errorMessage);
                        }

                        @Override
                        public void onDeleteComplete() {
                            Log.d(TAG, "图片删除操作完成");
                        }
                    });
        }
    }

    /**
     * 获取当前图片数量（不包括添加按钮）
     */
    private int getCurrentImageCount() {
        int count = 0;
        for (ImageItem item : imageItems) {
            if (item.getType() == ImageItem.Type.IMAGE) {
                count++;
            }
        }
        return count;
    }

    /**
     * 更新添加按钮显示
     */
    private void updateAddButton() {
        removeAddButton();
        // 允许在新增模式和编辑模式下都可以添加图片
        if (getCurrentImageCount() < MAX_IMAGES) {
            imageItems.add(new ImageItem(ImageItem.Type.ADD_BUTTON));
        }
    }

    /**
     * 移除添加按钮
     */
    private void removeAddButton() {
        imageItems.removeIf(item -> item.getType() == ImageItem.Type.ADD_BUTTON);
    }

    /**
     * 更新已上传的图片
     */
    private void updateUploadedImage(String imageUrl) {
        Log.d(TAG, "updateUploadedImage: 更新上传成功的图片URL: " + imageUrl);

        // 处理图片URL，确保包含完整的服务器地址
        String processedUrl = processImageUrl(imageUrl);
        Log.d(TAG, "updateUploadedImage: 处理后的URL: " + processedUrl);

        // 找到第一个正在上传的图片并更新它
        // 在多图片上传时，按顺序更新每个成功上传的图片
        for (int i = 0; i < imageItems.size(); i++) {
            ImageItem item = imageItems.get(i);
            if (item.isUploading() && item.getImageUri() != null && item.getImageUrl() == null) {
                Log.d(TAG, "updateUploadedImage: 找到上传中的图片，位置: " + i + "，更新为服务器URL");
                item.setImageUrl(processedUrl);
                item.setUploading(false);
                // 保留URI以便后续可能的操作，但主要显示服务器URL
                adapter.notifyItemChanged(i);
                break;
            }
        }
    }

    /**
     * 移除上传失败的图片
     */
    private void removeFailedUploads() {
        imageItems.removeIf(item -> item.isUploading());
        updateAddButton();
        adapter.notifyDataSetChanged();
    }

    /**
     * 显示消息
     */
    private void showMessage(String message) {
        Snackbar.make(recyclerView, message, Snackbar.LENGTH_SHORT).show();
    }

    /**
     * 处理图片URL，确保它是有效的完整URL
     *
     * @param imageUrl 原始图片URL
     * @return 处理后的完整URL
     */
    private String processImageUrl(String imageUrl) {
        if (imageUrl == null || imageUrl.trim().isEmpty()) {
            return "";
        }

        // 如果已经是完整的URL，直接返回
        if (imageUrl.startsWith("http://") || imageUrl.startsWith("https://")) {
            return imageUrl;
        }

        // 如果是相对路径，添加服务器基础URL
        String baseUrl = com.opms.common.constants.EnvironmentConfig.getBaseUrl();

        // 确保路径格式正确
        String normalizedPath = imageUrl.replace("\\", "/");

        // 确保baseUrl以/结尾，路径不以/开头，避免双斜杠
        if (!baseUrl.endsWith("/")) {
            baseUrl += "/";
        }
        if (normalizedPath.startsWith("/")) {
            normalizedPath = normalizedPath.substring(1);
        }

        return baseUrl + normalizedPath;
    }

    /**
     * 图片操作监听器
     */
    public interface OnImageActionListener {
        void onAddImageClick();

        void onImageClick(ImageItem item, int position);

        void onImageLongClick(ImageItem item, int position);

        void onImageDelete(ImageItem item, int position);

        void onImageUploadProgress(int uploadedCount, int totalCount);

        void onImageUploadComplete(List<String> successUrls, List<String> failureMessages);
    }

    /**
     * 图片项数据类
     */
    public static class ImageItem {
        private String imageId;
        private Type type;
        private String imageUrl;
        private Uri imageUri;
        private boolean isUploading;

        public ImageItem(String imageId, String imageUrl) {
            this.type = Type.IMAGE;
            this.imageId = imageId;
            this.imageUrl = imageUrl;
        }

        public String getImageId() {
            return imageId;
        }

        public ImageItem(Type type) {
            this.type = type;
        }

        public ImageItem(String imageUrl) {
            this.type = Type.IMAGE;
            this.imageUrl = imageUrl;
        }

        public ImageItem(Uri imageUri) {
            this.type = Type.IMAGE;
            this.imageUri = imageUri;
        }

        public void setImageId(String imageId) {
            this.imageId = imageId;
        }

        // Getters and setters
        public Type getType() {
            return type;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public Uri getImageUri() {
            return imageUri;
        }

        public boolean isUploading() {
            return isUploading;
        }

        public void setUploading(boolean uploading) {
            isUploading = uploading;
        }

        public enum Type {
            IMAGE,      // 已有图片
            ADD_BUTTON  // 添加按钮
        }
    }

    /**
     * 图片适配器
     */
    private class ImageAdapter extends RecyclerView.Adapter<ImageAdapter.ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(context).inflate(R.layout.item_multi_image, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            ImageItem item = imageItems.get(position);
            holder.bind(item, position);
        }

        @Override
        public int getItemCount() {
            return imageItems.size();
        }

        class ViewHolder extends RecyclerView.ViewHolder {
            MaterialCardView cardView;
            ImageView imageView;
            View uploadingOverlay;
            View deleteButton;

            ViewHolder(@NonNull View itemView) {
                super(itemView);
                cardView = itemView.findViewById(R.id.card_image);
                imageView = itemView.findViewById(R.id.iv_image);
                uploadingOverlay = itemView.findViewById(R.id.view_uploading_overlay);
                deleteButton = itemView.findViewById(R.id.btn_delete);
            }

            void bind(ImageItem item, int position) {
                if (item.getType() == ImageItem.Type.ADD_BUTTON) {
                    // 显示添加按钮
                    imageView.setImageResource(R.drawable.ic_add_photo);
                    uploadingOverlay.setVisibility(View.GONE);
                    deleteButton.setVisibility(View.GONE);

                    cardView.setOnClickListener(v -> {
                        if (listener != null) {
                            listener.onAddImageClick();
                        }
                    });
                } else {
                    // 显示图片
                    if (item.getImageUrl() != null) {
                        // 服务器图片
                        Glide.with(context)
                                .load(item.getImageUrl())
                                .placeholder(R.drawable.ic_image_placeholder)
                                .error(R.drawable.ic_image_error)
                                .centerCrop()
                                .into(imageView);
                    } else if (item.getImageUri() != null) {
                        // 本地图片
                        Glide.with(context)
                                .load(item.getImageUri())
                                .placeholder(R.drawable.ic_image_placeholder)
                                .error(R.drawable.ic_image_error)
                                .centerCrop()
                                .into(imageView);
                    }

                    // 上传状态覆盖层
                    uploadingOverlay.setVisibility(item.isUploading() ? View.VISIBLE : View.GONE);

                    // 删除按钮 - 在新增模式和编辑模式下都允许删除
                    deleteButton.setVisibility(View.VISIBLE);
                    deleteButton.setOnClickListener(v -> {
                        if (listener != null) {
                            listener.onImageDelete(item, position);
                        }
                    });

                    // 图片点击事件
                    cardView.setOnClickListener(v -> {
                        if (listener != null) {
                            listener.onImageClick(item, position);
                        }
                    });

                    // 图片长按事件
                    cardView.setOnLongClickListener(v -> {
                        if (listener != null) {
                            listener.onImageLongClick(item, position);
                        }
                        return true;
                    });
                }
            }
        }
    }
}
