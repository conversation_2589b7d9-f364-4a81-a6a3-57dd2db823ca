package com.opms.ui.business;

import com.opms.data.repository.ComponentRepository;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.data.repository.ProductRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductEditActivity_MembersInjector implements MembersInjector<ProductEditActivity> {
  private final Provider<ProductRepository> productRepositoryProvider;

  private final Provider<ImageUploadRepository> imageUploadRepositoryProvider;

  private final Provider<ComponentRepository> componentRepositoryProvider;

  public ProductEditActivity_MembersInjector(Provider<ProductRepository> productRepositoryProvider,
      Provider<ImageUploadRepository> imageUploadRepositoryProvider,
      Provider<ComponentRepository> componentRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
    this.imageUploadRepositoryProvider = imageUploadRepositoryProvider;
    this.componentRepositoryProvider = componentRepositoryProvider;
  }

  public static MembersInjector<ProductEditActivity> create(
      Provider<ProductRepository> productRepositoryProvider,
      Provider<ImageUploadRepository> imageUploadRepositoryProvider,
      Provider<ComponentRepository> componentRepositoryProvider) {
    return new ProductEditActivity_MembersInjector(productRepositoryProvider, imageUploadRepositoryProvider, componentRepositoryProvider);
  }

  @Override
  public void injectMembers(ProductEditActivity instance) {
    injectProductRepository(instance, productRepositoryProvider.get());
    injectImageUploadRepository(instance, imageUploadRepositoryProvider.get());
    injectComponentRepository(instance, componentRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.opms.ui.business.ProductEditActivity.productRepository")
  public static void injectProductRepository(ProductEditActivity instance,
      ProductRepository productRepository) {
    instance.productRepository = productRepository;
  }

  @InjectedFieldSignature("com.opms.ui.business.ProductEditActivity.imageUploadRepository")
  public static void injectImageUploadRepository(ProductEditActivity instance,
      ImageUploadRepository imageUploadRepository) {
    instance.imageUploadRepository = imageUploadRepository;
  }

  @InjectedFieldSignature("com.opms.ui.business.ProductEditActivity.componentRepository")
  public static void injectComponentRepository(ProductEditActivity instance,
      ComponentRepository componentRepository) {
    instance.componentRepository = componentRepository;
  }
}
