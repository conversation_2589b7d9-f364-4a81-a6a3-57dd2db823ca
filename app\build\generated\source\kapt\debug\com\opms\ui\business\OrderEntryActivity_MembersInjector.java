package com.opms.ui.business;

import com.opms.data.repository.OrderRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OrderEntryActivity_MembersInjector implements MembersInjector<OrderEntryActivity> {
  private final Provider<OrderRepository> orderRepositoryProvider;

  public OrderEntryActivity_MembersInjector(Provider<OrderRepository> orderRepositoryProvider) {
    this.orderRepositoryProvider = orderRepositoryProvider;
  }

  public static MembersInjector<OrderEntryActivity> create(
      Provider<OrderRepository> orderRepositoryProvider) {
    return new OrderEntryActivity_MembersInjector(orderRepositoryProvider);
  }

  @Override
  public void injectMembers(OrderEntryActivity instance) {
    injectOrderRepository(instance, orderRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.opms.ui.business.OrderEntryActivity.orderRepository")
  public static void injectOrderRepository(OrderEntryActivity instance,
      OrderRepository orderRepository) {
    instance.orderRepository = orderRepository;
  }
}
