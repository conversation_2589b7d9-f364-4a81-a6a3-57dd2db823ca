package com.opms.ui.business;

import com.opms.data.repository.ImageUploadRepository;
import com.opms.data.repository.OrderRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class OrderEntryActivity_MembersInjector implements MembersInjector<OrderEntryActivity> {
  private final Provider<OrderRepository> orderRepositoryProvider;

  private final Provider<ImageUploadRepository> imageUploadRepositoryProvider;

  public OrderEntryActivity_MembersInjector(Provider<OrderRepository> orderRepositoryProvider,
      Provider<ImageUploadRepository> imageUploadRepositoryProvider) {
    this.orderRepositoryProvider = orderRepositoryProvider;
    this.imageUploadRepositoryProvider = imageUploadRepositoryProvider;
  }

  public static MembersInjector<OrderEntryActivity> create(
      Provider<OrderRepository> orderRepositoryProvider,
      Provider<ImageUploadRepository> imageUploadRepositoryProvider) {
    return new OrderEntryActivity_MembersInjector(orderRepositoryProvider, imageUploadRepositoryProvider);
  }

  @Override
  public void injectMembers(OrderEntryActivity instance) {
    injectOrderRepository(instance, orderRepositoryProvider.get());
    injectImageUploadRepository(instance, imageUploadRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.opms.ui.business.OrderEntryActivity.orderRepository")
  public static void injectOrderRepository(OrderEntryActivity instance,
      OrderRepository orderRepository) {
    instance.orderRepository = orderRepository;
  }

  @InjectedFieldSignature("com.opms.ui.business.OrderEntryActivity.imageUploadRepository")
  public static void injectImageUploadRepository(OrderEntryActivity instance,
      ImageUploadRepository imageUploadRepository) {
    instance.imageUploadRepository = imageUploadRepository;
  }
}
