<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_order_entry" modulePackage="com.opms" filePath="app\src\main\res\layout\activity_order_entry.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.constraintlayout.widget.ConstraintLayout"><Targets><Target tag="layout/activity_order_entry_0" view="androidx.constraintlayout.widget.ConstraintLayout"><Expressions/><location startLine="1" startOffset="0" endLine="292" endOffset="51"/></Target><Target id="@+id/app_bar_layout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="8" startOffset="4" endLine="24" endOffset="53"/></Target><Target id="@+id/toolbar" view="androidx.appcompat.widget.Toolbar"><Expressions/><location startLine="16" startOffset="8" endLine="22" endOffset="66"/></Target><Target id="@+id/et_customer" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="50" startOffset="16" endLine="56" endOffset="46"/></Target><Target id="@+id/et_order_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="69" startOffset="16" endLine="75" endOffset="46"/></Target><Target id="@+id/et_order_user" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="88" startOffset="16" endLine="94" endOffset="46"/></Target><Target id="@+id/et_completion_date" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="107" startOffset="16" endLine="113" endOffset="46"/></Target><Target id="@+id/et_total_money" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="124" startOffset="16" endLine="130" endOffset="41"/></Target><Target id="@+id/et_order_money" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="141" startOffset="16" endLine="146" endOffset="41"/></Target><Target id="@+id/et_received_money" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="157" startOffset="16" endLine="162" endOffset="41"/></Target><Target id="@+id/et_remark" view="com.google.android.material.textfield.TextInputEditText"><Expressions/><location startLine="173" startOffset="16" endLine="180" endOffset="42"/></Target><Target id="@+id/btn_add_product" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="200" startOffset="16" endLine="206" endOffset="49"/></Target><Target id="@+id/rv_order_items" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="211" startOffset="12" endLine="218" endOffset="61"/></Target><Target id="@+id/rv_order_images" view="androidx.recyclerview.widget.RecyclerView"><Expressions/><location startLine="242" startOffset="20" endLine="246" endOffset="64"/></Target><Target id="@+id/btn_cancel" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="258" startOffset="16" endLine="265" endOffset="39"/></Target><Target id="@+id/btn_save" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="267" startOffset="16" endLine="273" endOffset="39"/></Target><Target id="@+id/progress_bar" view="ProgressBar"><Expressions/><location startLine="282" startOffset="4" endLine="290" endOffset="51"/></Target></Targets></Layout>