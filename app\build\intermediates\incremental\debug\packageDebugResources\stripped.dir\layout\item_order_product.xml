<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 产品信息行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- 产品信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_product_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="产品名称" />

                <TextView
                    android:id="@+id/tv_product_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@android:color/darker_gray"
                    android:textSize="14sp"
                    tools:text="产品编码: P001" />

                <TextView
                    android:id="@+id/tv_product_price"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    tools:text="单价: ¥100.00" />

            </LinearLayout>

            <!-- 删除按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_delete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:minWidth="0dp"
                android:text="删除"
                android:textColor="@color/design_default_color_error"
                app:icon="@drawable/ic_delete"
                app:iconTint="@color/design_default_color_error" />

        </LinearLayout>

        <!-- 数量输入行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="数量："
                android:textSize="14sp" />

            <!-- 减少按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_decrease"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="-"
                android:textSize="18sp" />

            <!-- 数量输入框 -->
            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/et_quantity"
                android:layout_width="80dp"
                android:layout_height="40dp"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/bg_quantity_input"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="4"
                android:text="1"
                android:textSize="16sp" />

            <!-- 增加按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_increase"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:insetLeft="0dp"
                android:insetTop="0dp"
                android:insetRight="0dp"
                android:insetBottom="0dp"
                android:minWidth="0dp"
                android:minHeight="0dp"
                android:text="+"
                android:textSize="18sp" />

            <!-- 小计 -->
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_weight="1"
                android:gravity="end"
                android:text="小计："
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tv_subtotal"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="¥100.00" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
