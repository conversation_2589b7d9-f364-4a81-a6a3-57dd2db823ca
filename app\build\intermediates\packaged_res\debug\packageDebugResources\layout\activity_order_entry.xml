<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.business.OrderEntryActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/app_bar_layout">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 客户选择 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="客户"
                app:endIconDrawable="@drawable/ic_arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_customer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 下单日期 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="下单日期"
                app:endIconDrawable="@drawable/ic_calendar"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_order_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 接单人员 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="接单人员"
                app:endIconDrawable="@drawable/ic_arrow_drop_down"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_order_user"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 要求完成日期 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="要求完成日期"
                app:endIconDrawable="@drawable/ic_calendar"
                app:endIconMode="custom">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_completion_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:clickable="true"
                    android:focusable="false"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 订单总额 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="订单总额（自动计算）">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_total_money"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:inputType="numberDecimal"
                    android:text="0.00" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 订单金额 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="订单金额（实际金额）">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_order_money"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal"
                    android:text="0.00" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 已收金额 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="已收金额">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_received_money"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal"
                    android:text="0.00" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 备注 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="备注">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_remark"
                    android:layout_width="match_parent"
                    android:layout_height="120dp"
                    android:gravity="top|start"
                    android:inputType="textMultiLine"
                    android:lines="4"
                    android:maxLines="4" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- 产品列表标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="订单产品"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_add_product"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="添加产品"
                    app:icon="@drawable/ic_add" />

            </LinearLayout>

            <!-- 产品列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_order_items"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:nestedScrollingEnabled="false"
                tools:itemCount="2"
                tools:listitem="@layout/item_order_product" />

            <!-- 订单图片 -->
            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="订单图片"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_order_images"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_cancel"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="取消" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="保存" />

            </LinearLayout>

        </LinearLayout>

    </ScrollView>

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
