package com.opms.ui.business.dialog;

import com.opms.data.repository.ProductRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class ProductSelectionDialog_MembersInjector implements MembersInjector<ProductSelectionDialog> {
  private final Provider<ProductRepository> productRepositoryProvider;

  public ProductSelectionDialog_MembersInjector(
      Provider<ProductRepository> productRepositoryProvider) {
    this.productRepositoryProvider = productRepositoryProvider;
  }

  public static MembersInjector<ProductSelectionDialog> create(
      Provider<ProductRepository> productRepositoryProvider) {
    return new ProductSelectionDialog_MembersInjector(productRepositoryProvider);
  }

  @Override
  public void injectMembers(ProductSelectionDialog instance) {
    injectProductRepository(instance, productRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.opms.ui.business.dialog.ProductSelectionDialog.productRepository")
  public static void injectProductRepository(ProductSelectionDialog instance,
      ProductRepository productRepository) {
    instance.productRepository = productRepository;
  }
}
