package com.opms.data.repository;

import com.opms.data.model.request.ProductRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ProductCompleteResponse;
import com.opms.data.model.response.ProductListResponse;
import com.opms.data.model.response.ProductResponse;
import com.opms.data.remote.ApiService;

import javax.inject.Inject;

import retrofit2.Call;

/**
 * 产品数据仓库实现类
 */
public class ProductRepositoryImpl implements ProductRepository {

    private final ApiService apiService;

    @Inject
    public ProductRepositoryImpl(ApiService apiService) {
        this.apiService = apiService;
    }

    @Override
    public Call<ApiResponse<ProductListResponse>> getProductList(int page, int size, String keyword) {
        return apiService.getProductList(page, size, keyword);
    }

    @Override
    public Call<ApiResponse<ProductCompleteResponse>> getProductDetail(int id) {
        return apiService.getProductDetail(id);
    }

    @Override
    public Call<ApiResponse<ProductResponse>> createProduct(ProductRequest request) {
        return apiService.createProduct(request);
    }

    @Override
    public Call<ApiResponse<ProductResponse>> updateProduct(int id, ProductRequest request) {
        return apiService.updateProduct(id, request);
    }

    @Override
    public Call<ApiResponse<Void>> deleteProduct(ProductRequest request) {
        return apiService.deleteProduct(request);
    }

    @Override
    public Call<ApiResponse<ProductResponse>> checkProductCodeExists(String code) {
        return apiService.checkProductCodeExists(code);
    }
}