<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="4dp"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 权限图标 -->
        <ImageView
            android:id="@+id/iv_permission_icon"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical"
            android:layout_marginEnd="16dp"
            android:background="@drawable/bg_icon_circle"
            android:padding="8dp"
            android:src="@drawable/ic_security"
            app:tint="@color/primary" />

        <!-- 权限信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 查看模式 -->
            <LinearLayout
                android:id="@+id/ll_view_mode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <!-- 权限名称和状态 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="@color/text_primary"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        tools:text="用户管理" />

                    <TextView
                        android:id="@+id/tv_type"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:background="@drawable/bg_type_menu"
                        android:paddingHorizontal="6dp"
                        android:paddingVertical="2dp"
                        android:text="菜单"
                        android:textColor="@color/white"
                        android:textSize="10sp"
                        tools:text="菜单" />

                    <TextView
                        android:id="@+id/tv_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="4dp"
                        android:paddingHorizontal="8dp"
                        android:paddingVertical="2dp"
                        android:text="启用"
                        android:textSize="12sp"
                        tools:background="@drawable/bg_status_active"
                        tools:textColor="@color/success" />

                </LinearLayout>

                <!-- 权限代码和路径 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_code"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp"
                        tools:text="USER_MANAGE" />

                    <TextView
                        android:id="@+id/tv_path"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:maxLines="1"
                        android:textColor="@color/text_hint"
                        android:textSize="12sp"
                        tools:text="/system/user" />

                </LinearLayout>

                <!-- 权限描述 -->
                <TextView
                    android:id="@+id/tv_description"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:textColor="@color/text_hint"
                    android:textSize="12sp"
                    tools:text="用户信息管理，包括新增、编辑、删除用户等功能" />


            </LinearLayout>

            <!-- 编辑模式 -->
            <LinearLayout
                android:id="@+id/ll_edit_mode"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- 编辑权限名称 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_edit_name"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="权限名称">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_edit_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLength="50" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 编辑权限描述 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_edit_description"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="权限描述">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_edit_description"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:maxLength="200"
                        android:maxLines="3" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 状态开关和操作按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="启用"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <com.google.android.material.switchmaterial.SwitchMaterial
                        android:id="@+id/switch_edit_status"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="8dp"
                        android:layout_marginEnd="16dp" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <!-- 保存按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_save"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="4dp"
                        android:minWidth="0dp"
                        android:text="保存"
                        android:textSize="12sp" />

                    <!-- 取消按钮 -->
                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_cancel"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:minWidth="0dp"
                        android:text="取消"
                        android:textSize="12sp" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 删除按钮 -->
        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:padding="4dp"
            android:src="@drawable/ic_delete"
            app:tint="@color/error" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
