<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/card_component"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 左列：基本信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingEnd="8dp">

            <!-- 部件名称 -->
            <TextView
                android:id="@+id/tv_component_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="部件名称"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 型号 -->
            <TextView
                android:id="@+id/tv_component_model"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="型号: Model-A"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 备注 -->
            <TextView
                android:id="@+id/tv_component_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="备注: 备注信息"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 右列：规格、备注和操作 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:paddingStart="8dp">

            <!-- 部件编码 -->
            <TextView
                android:id="@+id/tv_component_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="编码: COMP001"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 规格 -->
            <TextView
                android:id="@+id/tv_component_standard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="规格: Standard-1"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:gravity="end"
                android:orientation="horizontal">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_edit"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="编辑"
                    app:icon="@drawable/ic_edit" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_delete"
                    style="@style/Widget.Material3.Button.TextButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="删除"
                    android:textColor="@color/error"
                    app:icon="@drawable/ic_delete"
                    app:iconTint="@color/error" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
