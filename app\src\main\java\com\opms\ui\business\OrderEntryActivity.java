package com.opms.ui.business;

import android.app.DatePickerDialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.snackbar.Snackbar;
import com.opms.R;
import com.opms.common.enums.OrderStatus;
import com.opms.data.model.request.OrderItemRequest;
import com.opms.data.model.request.OrderRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.CustomerResponse;
import com.opms.data.model.response.OrderResponse;
import com.opms.data.model.response.UserResponse;
import com.opms.data.repository.OrderRepository;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.common.enums.BusinessImgType;
import com.opms.common.utils.MultiImageManager;
import com.opms.databinding.ActivityOrderEntryBinding;
import com.opms.ui.business.adapter.OrderItemAdapter;
import com.opms.ui.business.dialog.CustomerSelectionDialog;
import com.opms.ui.business.dialog.ProductSelectionDialog;
import com.opms.ui.business.dialog.UserSelectionDialog;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import javax.inject.Inject;

import dagger.hilt.android.AndroidEntryPoint;
import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

@AndroidEntryPoint
public class OrderEntryActivity extends AppCompatActivity {

    private static final String TAG = "OrderEntry";

    @Inject
    OrderRepository orderRepository;

    @Inject
    ImageUploadRepository imageUploadRepository;

    private ActivityOrderEntryBinding binding;
    private OrderItemAdapter orderItemAdapter;
    private MultiImageManager imageManager;
    private DecimalFormat decimalFormat = new DecimalFormat("#0.00");
    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());

    // 选中的数据
    private CustomerResponse selectedCustomer;
    private UserResponse selectedOrderUser;
    private String tempOrderId; // 临时订单ID，用于新增模式下的图片上传

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityOrderEntryBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setupToolbar();
        setupRecyclerView();
        setupClickListeners();
        setupTextWatchers();
        setupImageManager();
        initializeDefaultValues();
    }

    private void setupToolbar() {
        setSupportActionBar(binding.toolbar);
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("订单录入");
        }
    }

    private void setupRecyclerView() {
        orderItemAdapter = new OrderItemAdapter();
        orderItemAdapter.setOnOrderItemChangeListener(new OrderItemAdapter.OnOrderItemChangeListener() {
            @Override
            public void onQuantityChanged() {
                updateTotalAmount();
            }

            @Override
            public void onItemRemoved() {
                updateTotalAmount();
            }
        });

        binding.rvOrderItems.setLayoutManager(new LinearLayoutManager(this));
        binding.rvOrderItems.setAdapter(orderItemAdapter);
    }

    private void setupClickListeners() {
        // 客户选择
        binding.etCustomer.setOnClickListener(v -> showCustomerSelectionDialog());

        // 下单日期选择
        binding.etOrderDate.setOnClickListener(v -> showDatePicker(binding.etOrderDate, "选择下单日期"));

        // 接单人员选择
        binding.etOrderUser.setOnClickListener(v -> showUserSelectionDialog());

        // 要求完成日期选择
        binding.etCompletionDate.setOnClickListener(v -> showDatePicker(binding.etCompletionDate, "选择要求完成日期"));

        // 添加产品
        binding.btnAddProduct.setOnClickListener(v -> showProductSelectionDialog());

        // 保存按钮
        binding.btnSave.setOnClickListener(v -> saveOrder());

        // 取消按钮
        binding.btnCancel.setOnClickListener(v -> finish());
    }

    private void setupTextWatchers() {
        // 订单金额变化时同步到订单总额（如果用户没有手动修改）
        binding.etOrderMoney.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                // 这里可以添加订单金额变化的逻辑
            }
        });
    }

    private void setupImageManager() {
        Log.d(TAG, "setupImageManager: 设置多图片管理器");

        // 生成临时订单ID用于新增模式
        tempOrderId = "temp_order_" + System.currentTimeMillis();

        // 初始化多图片管理器
        imageManager = new MultiImageManager(this, binding.rvOrderImages);

        // 设置配置 - 新增模式下使用临时ID
        imageManager.setup(
                imageUploadRepository,
                BusinessImgType.ORDER,
                tempOrderId,
                getCurrentUser(),
                false // 新增模式
        );
    }

    private void initializeDefaultValues() {
        // 设置默认下单日期为今天
        String today = dateFormat.format(new Date());
        binding.etOrderDate.setText(today);

        // 初始化金额为0
        binding.etTotalMoney.setText("0.00");
        binding.etOrderMoney.setText("0.00");
        binding.etReceivedMoney.setText("0.00");
    }

    private void showCustomerSelectionDialog() {
        CustomerSelectionDialog dialog = CustomerSelectionDialog.newInstance();
        dialog.setOnCustomerSelectedListener(customer -> {
            selectedCustomer = customer;
            binding.etCustomer.setText(customer.getName() + " (" + customer.getCode() + ")");
        });
        dialog.show(getSupportFragmentManager(), "CustomerSelectionDialog");
    }

    private void showUserSelectionDialog() {
        UserSelectionDialog dialog = UserSelectionDialog.newInstance();
        dialog.setOnUserSelectedListener(user -> {
            selectedOrderUser = user;
            binding.etOrderUser.setText(user.getName() + " (" + user.getUsername() + ")");
        });
        dialog.show(getSupportFragmentManager(), "UserSelectionDialog");
    }

    private void showProductSelectionDialog() {
        ProductSelectionDialog dialog = ProductSelectionDialog.newInstance();
        dialog.setOnProductSelectedListener(product -> {
            orderItemAdapter.addOrderItem(product);
            updateTotalAmount();
        });
        dialog.show(getSupportFragmentManager(), "ProductSelectionDialog");
    }

    private void showDatePicker(com.google.android.material.textfield.TextInputEditText editText, String title) {
        Calendar calendar = Calendar.getInstance();

        // 如果输入框已有日期，则使用该日期作为初始值
        String currentText = editText.getText().toString().trim();
        if (!TextUtils.isEmpty(currentText)) {
            try {
                Date date = dateFormat.parse(currentText);
                if (date != null) {
                    calendar.setTime(date);
                }
            } catch (Exception e) {
                // 使用当前日期
            }
        }

        DatePickerDialog datePickerDialog = new DatePickerDialog(
                this,
                (view, year, month, dayOfMonth) -> {
                    calendar.set(year, month, dayOfMonth);
                    String selectedDate = dateFormat.format(calendar.getTime());
                    editText.setText(selectedDate);
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        datePickerDialog.setTitle(title);
        datePickerDialog.show();
    }

    private void updateTotalAmount() {
        double totalAmount = orderItemAdapter.getTotalAmount();
        binding.etTotalMoney.setText(decimalFormat.format(totalAmount));

        // 如果订单金额还是默认值或者为空，则同步更新
        String currentOrderMoney = binding.etOrderMoney.getText().toString().trim();
        try {
            if ("0.00".equals(currentOrderMoney) || TextUtils.isEmpty(currentOrderMoney) ||
                Double.parseDouble(currentOrderMoney) == 0.0) {
                binding.etOrderMoney.setText(decimalFormat.format(totalAmount));
            }
        } catch (NumberFormatException e) {
            // 如果解析失败，也同步更新
            binding.etOrderMoney.setText(decimalFormat.format(totalAmount));
        }
    }

    private void saveOrder() {
        if (!validateInput()) {
            return;
        }

        OrderRequest request = createOrderRequest();
        showLoading(true);

        Call<ApiResponse<OrderResponse>> call = orderRepository.createOrder(request);
        call.enqueue(new Callback<ApiResponse<OrderResponse>>() {
            @Override
            public void onResponse(@NonNull Call<ApiResponse<OrderResponse>> call,
                                   @NonNull Response<ApiResponse<OrderResponse>> response) {
                showLoading(false);

                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<OrderResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        OrderResponse newOrder = apiResponse.getData();
                        if (newOrder != null && newOrder.getId() > 0) {
                            // 更新图片的业务ID关联
                            if (imageManager != null) {
                                Log.d(TAG, "订单创建成功，ID: " + newOrder.getId() + "，更新图片业务ID关联");
                                imageManager.updateBusinessId(String.valueOf(newOrder.getId()));
                            }
                        }

                        Snackbar.make(binding.getRoot(), "订单录入成功", Snackbar.LENGTH_SHORT).show();
                        setResult(RESULT_OK);
                        finish();
                    } else {
                        showError(apiResponse.getMessage() != null ? apiResponse.getMessage() : "订单录入失败");
                    }
                } else {
                    showError("订单录入失败");
                }
            }

            @Override
            public void onFailure(@NonNull Call<ApiResponse<OrderResponse>> call, @NonNull Throwable t) {
                showLoading(false);
                Log.e(TAG, "订单录入失败: " + t.getMessage(), t);
                showError("网络错误: " + t.getMessage());
            }
        });
    }

    private boolean validateInput() {
        // 客户验证
        if (selectedCustomer == null) {
            showError("请选择客户");
            return false;
        }

        // 下单日期验证
        String orderDate = binding.etOrderDate.getText().toString().trim();
        if (TextUtils.isEmpty(orderDate)) {
            showError("请选择下单日期");
            return false;
        }

        // 接单人员验证
        if (selectedOrderUser == null) {
            showError("请选择接单人员");
            return false;
        }

        // 要求完成日期验证
        String completionDate = binding.etCompletionDate.getText().toString().trim();
        if (TextUtils.isEmpty(completionDate)) {
            showError("请选择要求完成日期");
            return false;
        }

        // 产品列表验证
        if (orderItemAdapter.getOrderItems().isEmpty()) {
            showError("请至少添加一个产品");
            return false;
        }

        return true;
    }

    private OrderRequest createOrderRequest() {
        OrderRequest request = new OrderRequest();

        // 基本信息
        request.setCustomerCode(selectedCustomer.getCode());
        request.setOrderDate(binding.etOrderDate.getText().toString().trim());
        request.setOrderUser(selectedOrderUser.getUsername());
        request.setCompletionDate(binding.etCompletionDate.getText().toString().trim());
        request.setStatus(OrderStatus.PENDING.getCode()); // 默认状态为未处理
        request.setRemark(binding.etRemark.getText().toString().trim());

        // 金额信息
        try {
            double totalMoney = Double.parseDouble(binding.etTotalMoney.getText().toString().trim());
            double orderMoney = Double.parseDouble(binding.etOrderMoney.getText().toString().trim());
            double receivedMoney = Double.parseDouble(binding.etReceivedMoney.getText().toString().trim());

            request.setTotalMoney(totalMoney);
            request.setOrderMoney(orderMoney);
            request.setReceivedMoney(receivedMoney);
        } catch (NumberFormatException e) {
            request.setTotalMoney(0.0);
            request.setOrderMoney(0.0);
            request.setReceivedMoney(0.0);
        }

        // 订单项列表
        List<OrderItemRequest> items = new ArrayList<>();
        for (OrderItemAdapter.OrderItem orderItem : orderItemAdapter.getOrderItems()) {
            OrderItemRequest itemRequest = new OrderItemRequest();
            itemRequest.setProductId(orderItem.getProduct().getId());
            itemRequest.setNumber(orderItem.getQuantity());
            itemRequest.setStatus(OrderStatus.PENDING.getCode()); // 默认状态为未处理
            items.add(itemRequest);
        }
        request.setItems(items);

        return request;
    }

    private void showLoading(boolean show) {
        binding.progressBar.setVisibility(show ? View.VISIBLE : View.GONE);
        binding.btnSave.setEnabled(!show);
    }

    private void showError(String message) {
        Snackbar.make(binding.getRoot(), message, Snackbar.LENGTH_LONG).show();
    }

    private String getCurrentUser() {
        // 这里应该从SharedPreferences或其他地方获取当前用户信息
        // 暂时返回一个默认值
        return "current_user";
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void finish() {
        super.finish();
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }
}
