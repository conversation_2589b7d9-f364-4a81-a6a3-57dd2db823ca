<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.PermissionEditActivity">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="添加权限"
        app:titleTextColor="@color/white" />

    <!-- 加载进度条 -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/progress_loading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <!-- 滚动内容 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progress_loading">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 权限基本信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 卡片标题 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="权限基本信息"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- 权限名称 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_name"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="权限名称"
                        app:counterEnabled="true"
                        app:counterMaxLength="50"
                        app:endIconMode="clear_text">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLength="50" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 权限代码 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_code"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="权限代码"
                        app:counterEnabled="true"
                        app:counterMaxLength="30"
                        app:endIconMode="clear_text"
                        app:helperText="权限的唯一标识，建议使用英文字母和下划线">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_code"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textNoSuggestions"
                            android:maxLength="30" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 权限描述 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_description"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="权限描述"
                        app:counterEnabled="true"
                        app:counterMaxLength="200"
                        app:endIconMode="clear_text">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_description"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="top"
                            android:inputType="textMultiLine"
                            android:maxLength="200"
                            android:minLines="3" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 状态开关 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="启用状态"
                            android:textColor="@color/text_primary"
                            android:textSize="16sp" />

                        <com.google.android.material.switchmaterial.SwitchMaterial
                            android:id="@+id/switch_status"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:checked="true" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 业务模块权限卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 卡片标题 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="业务模块权限"
                            android:textColor="@color/text_primary"
                            android:textSize="18sp"
                            android:textStyle="bold" />

                        <!-- 全选/全不选按钮 -->
                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_select_all"
                            style="@style/Widget.MaterialComponents.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:minWidth="0dp"
                            android:text="全选"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <!-- 业务模块列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_business_modules"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:itemCount="5"
                        tools:listitem="@layout/item_business_module_checkbox" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- 取消按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_cancel"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:layout_weight="1"
                    android:text="取消" />

                <!-- 保存按钮 -->
                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:layout_weight="1"
                    android:text="保存" />

            </LinearLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>
