<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.business.ProductEditActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <!-- 产品图片 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_images"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="产品图片"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_product_images"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false" />

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 产品基本信息 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_basic_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/card_images">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="基本信息"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <!-- 产品名称 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="产品名称 *"
                        app:startIconDrawable="@drawable/ic_product_management">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_product_name"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 产品编码 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_product_code"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="产品编码 *"
                        app:startIconDrawable="@drawable/ic_code">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_product_code"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 型号 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="型号"
                        app:startIconDrawable="@drawable/ic_model">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_product_model"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 规格 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="规格"
                        app:startIconDrawable="@drawable/ic_standard">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_product_standard"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 价格 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="价格 *"
                        app:startIconDrawable="@drawable/ic_work">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_product_price"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="numberDecimal"
                            android:maxLines="1" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 状态 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:hint="状态"
                        app:startIconDrawable="@drawable/ic_settings">

                        <AutoCompleteTextView
                            android:id="@+id/spinner_status"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="none"
                            android:text="启用" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <!-- 备注 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="备注"
                        app:startIconDrawable="@drawable/ic_remark">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_product_remark"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:maxLines="3"
                            android:minLines="2" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 产品组件信息 -->
            <androidx.cardview.widget.CardView
                android:id="@+id/card_component_info"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                app:cardCornerRadius="8dp"
                app:cardElevation="4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/card_basic_info">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 组件信息标题和操作按钮 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:orientation="horizontal">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="产品组件"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btn_add_component"
                            style="@style/Widget.Material3.Button.TextButton.Icon"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="添加组件"
                            app:icon="@drawable/ic_add" />

                    </LinearLayout>

                    <!-- 组件列表容器 -->
                    <LinearLayout
                        android:id="@+id/ll_component_list"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="vertical" />

                    <!-- 空状态提示 -->
                    <LinearLayout
                        android:id="@+id/ll_component_empty"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:padding="32dp"
                        android:visibility="gone">

                        <ImageView
                            android:layout_width="48dp"
                            android:layout_height="48dp"
                            android:alpha="0.5"
                            android:src="@drawable/ic_component_management" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="8dp"
                            android:text="暂无组件信息"
                            android:textColor="@color/text_secondary"
                            android:textSize="14sp" />

                    </LinearLayout>

                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <!-- 操作按钮 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:orientation="horizontal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/card_component_info">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_cancel"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="取消" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/btn_save"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="保存"
                    app:icon="@drawable/ic_save" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
