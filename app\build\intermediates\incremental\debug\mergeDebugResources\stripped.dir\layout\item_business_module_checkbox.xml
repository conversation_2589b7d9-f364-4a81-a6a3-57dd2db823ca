<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginVertical="4dp"
    android:clickable="true"
    android:focusable="true"
    app:cardCornerRadius="6dp"
    app:cardElevation="1dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:paddingVertical="12dp">

        <!-- 业务模块图标 -->
        <ImageView
            android:id="@+id/iv_module_icon"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginEnd="12dp"
            android:background="@drawable/bg_icon_circle_small"
            android:padding="6dp"
            android:src="@drawable/ic_business"
            app:tint="@color/primary" />

        <!-- 业务模块信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 模块名称 -->
            <TextView
                android:id="@+id/tv_module_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="客户管理" />

            <!-- 模块代码 -->
            <TextView
                android:id="@+id/tv_module_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:textColor="@color/text_secondary"
                android:textSize="12sp"
                tools:text="customer" />

        </LinearLayout>

        <!-- 复选框 -->
        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/cb_module"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:clickable="false"
            android:focusable="false" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
