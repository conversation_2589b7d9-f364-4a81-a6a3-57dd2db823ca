// Generated by view binder compiler. Do not edit!
package com.opms.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ProgressBar;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.button.MaterialButton;
import com.google.android.material.textfield.TextInputEditText;
import com.opms.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityOrderEntryBinding implements ViewBinding {
  @NonNull
  private final ConstraintLayout rootView;

  @NonNull
  public final AppBarLayout appBarLayout;

  @NonNull
  public final MaterialButton btnAddProduct;

  @NonNull
  public final MaterialButton btnCancel;

  @NonNull
  public final MaterialButton btnSave;

  @NonNull
  public final TextInputEditText etCompletionDate;

  @NonNull
  public final TextInputEditText etCustomer;

  @NonNull
  public final TextInputEditText etOrderDate;

  @NonNull
  public final TextInputEditText etOrderMoney;

  @NonNull
  public final TextInputEditText etOrderUser;

  @NonNull
  public final TextInputEditText etReceivedMoney;

  @NonNull
  public final TextInputEditText etRemark;

  @NonNull
  public final TextInputEditText etTotalMoney;

  @NonNull
  public final ProgressBar progressBar;

  @NonNull
  public final RecyclerView rvOrderImages;

  @NonNull
  public final RecyclerView rvOrderItems;

  @NonNull
  public final Toolbar toolbar;

  private ActivityOrderEntryBinding(@NonNull ConstraintLayout rootView,
      @NonNull AppBarLayout appBarLayout, @NonNull MaterialButton btnAddProduct,
      @NonNull MaterialButton btnCancel, @NonNull MaterialButton btnSave,
      @NonNull TextInputEditText etCompletionDate, @NonNull TextInputEditText etCustomer,
      @NonNull TextInputEditText etOrderDate, @NonNull TextInputEditText etOrderMoney,
      @NonNull TextInputEditText etOrderUser, @NonNull TextInputEditText etReceivedMoney,
      @NonNull TextInputEditText etRemark, @NonNull TextInputEditText etTotalMoney,
      @NonNull ProgressBar progressBar, @NonNull RecyclerView rvOrderImages,
      @NonNull RecyclerView rvOrderItems, @NonNull Toolbar toolbar) {
    this.rootView = rootView;
    this.appBarLayout = appBarLayout;
    this.btnAddProduct = btnAddProduct;
    this.btnCancel = btnCancel;
    this.btnSave = btnSave;
    this.etCompletionDate = etCompletionDate;
    this.etCustomer = etCustomer;
    this.etOrderDate = etOrderDate;
    this.etOrderMoney = etOrderMoney;
    this.etOrderUser = etOrderUser;
    this.etReceivedMoney = etReceivedMoney;
    this.etRemark = etRemark;
    this.etTotalMoney = etTotalMoney;
    this.progressBar = progressBar;
    this.rvOrderImages = rvOrderImages;
    this.rvOrderItems = rvOrderItems;
    this.toolbar = toolbar;
  }

  @Override
  @NonNull
  public ConstraintLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityOrderEntryBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityOrderEntryBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_order_entry, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityOrderEntryBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.app_bar_layout;
      AppBarLayout appBarLayout = ViewBindings.findChildViewById(rootView, id);
      if (appBarLayout == null) {
        break missingId;
      }

      id = R.id.btn_add_product;
      MaterialButton btnAddProduct = ViewBindings.findChildViewById(rootView, id);
      if (btnAddProduct == null) {
        break missingId;
      }

      id = R.id.btn_cancel;
      MaterialButton btnCancel = ViewBindings.findChildViewById(rootView, id);
      if (btnCancel == null) {
        break missingId;
      }

      id = R.id.btn_save;
      MaterialButton btnSave = ViewBindings.findChildViewById(rootView, id);
      if (btnSave == null) {
        break missingId;
      }

      id = R.id.et_completion_date;
      TextInputEditText etCompletionDate = ViewBindings.findChildViewById(rootView, id);
      if (etCompletionDate == null) {
        break missingId;
      }

      id = R.id.et_customer;
      TextInputEditText etCustomer = ViewBindings.findChildViewById(rootView, id);
      if (etCustomer == null) {
        break missingId;
      }

      id = R.id.et_order_date;
      TextInputEditText etOrderDate = ViewBindings.findChildViewById(rootView, id);
      if (etOrderDate == null) {
        break missingId;
      }

      id = R.id.et_order_money;
      TextInputEditText etOrderMoney = ViewBindings.findChildViewById(rootView, id);
      if (etOrderMoney == null) {
        break missingId;
      }

      id = R.id.et_order_user;
      TextInputEditText etOrderUser = ViewBindings.findChildViewById(rootView, id);
      if (etOrderUser == null) {
        break missingId;
      }

      id = R.id.et_received_money;
      TextInputEditText etReceivedMoney = ViewBindings.findChildViewById(rootView, id);
      if (etReceivedMoney == null) {
        break missingId;
      }

      id = R.id.et_remark;
      TextInputEditText etRemark = ViewBindings.findChildViewById(rootView, id);
      if (etRemark == null) {
        break missingId;
      }

      id = R.id.et_total_money;
      TextInputEditText etTotalMoney = ViewBindings.findChildViewById(rootView, id);
      if (etTotalMoney == null) {
        break missingId;
      }

      id = R.id.progress_bar;
      ProgressBar progressBar = ViewBindings.findChildViewById(rootView, id);
      if (progressBar == null) {
        break missingId;
      }

      id = R.id.rv_order_images;
      RecyclerView rvOrderImages = ViewBindings.findChildViewById(rootView, id);
      if (rvOrderImages == null) {
        break missingId;
      }

      id = R.id.rv_order_items;
      RecyclerView rvOrderItems = ViewBindings.findChildViewById(rootView, id);
      if (rvOrderItems == null) {
        break missingId;
      }

      id = R.id.toolbar;
      Toolbar toolbar = ViewBindings.findChildViewById(rootView, id);
      if (toolbar == null) {
        break missingId;
      }

      return new ActivityOrderEntryBinding((ConstraintLayout) rootView, appBarLayout, btnAddProduct,
          btnCancel, btnSave, etCompletionDate, etCustomer, etOrderDate, etOrderMoney, etOrderUser,
          etReceivedMoney, etRemark, etTotalMoney, progressBar, rvOrderImages, rvOrderItems,
          toolbar);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
