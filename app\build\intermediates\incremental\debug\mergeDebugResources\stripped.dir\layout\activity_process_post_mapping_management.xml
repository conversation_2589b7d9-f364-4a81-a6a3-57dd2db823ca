<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.ProcessPostMappingManagementActivity">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:theme="@style/ThemeOverlay.Material3.Dark.ActionBar"
        app:layout_constraintTop_toTopOf="parent"
        app:popupTheme="@style/ThemeOverlay.Material3.Light"
        app:title="流程岗位映射" />

    <!-- 进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <!-- 工具栏切换按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/btn_toggle_toolbar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:contentDescription="展开/收起工具栏"
        android:src="@drawable/ic_expand_more"
        app:fabSize="mini"
        app:layout_constraintEnd_toStartOf="@id/btn_toggle_search"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:tint="@color/white" />

    <!-- 快速搜索按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/btn_toggle_search"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:contentDescription="快速搜索"
        android:src="@drawable/ic_search"
        app:fabSize="mini"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar"
        app:tint="@color/white" />

    <!-- 可展开的工具栏 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/card_expandable_tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:visibility="gone"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintTop_toBottomOf="@id/btn_toggle_toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 映射数量显示 -->
            <TextView
                android:id="@+id/tv_mapping_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:text="共 0 条映射"
                android:textColor="@color/text_primary"
                android:textSize="14sp"
                android:textStyle="bold" />

            <!-- 搜索框 -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="搜索流程或岗位名称"
                app:endIconDrawable="@drawable/ic_search"
                app:endIconMode="custom"
                app:startIconDrawable="@drawable/ic_search">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/et_search"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 映射列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_expandable_tools">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_mappings"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingStart="8dp"
            android:paddingTop="8dp"
            android:paddingEnd="8dp"
            android:paddingBottom="80dp"
            tools:listitem="@layout/item_process_post_mapping" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 空状态视图 -->
    <LinearLayout
        android:id="@+id/layout_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:alpha="0.3"
            android:src="@drawable/ic_process_post_mapping"
            app:tint="@color/text_hint" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="暂无流程岗位映射"
            android:textColor="@color/text_hint"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="点击右下角按钮添加流程岗位映射"
            android:textColor="@color/text_hint"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 浮动操作按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:contentDescription="添加流程岗位映射"
        android:src="@drawable/ic_add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:tint="@color/white" />

</androidx.constraintlayout.widget.ConstraintLayout>
