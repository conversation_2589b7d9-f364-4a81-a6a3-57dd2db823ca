package com.opms.ui.business.dialog;

import com.opms.data.repository.CustomerRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class CustomerSelectionDialog_MembersInjector implements MembersInjector<CustomerSelectionDialog> {
  private final Provider<CustomerRepository> customerRepositoryProvider;

  public CustomerSelectionDialog_MembersInjector(
      Provider<CustomerRepository> customerRepositoryProvider) {
    this.customerRepositoryProvider = customerRepositoryProvider;
  }

  public static MembersInjector<CustomerSelectionDialog> create(
      Provider<CustomerRepository> customerRepositoryProvider) {
    return new CustomerSelectionDialog_MembersInjector(customerRepositoryProvider);
  }

  @Override
  public void injectMembers(CustomerSelectionDialog instance) {
    injectCustomerRepository(instance, customerRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.opms.ui.business.dialog.CustomerSelectionDialog.customerRepository")
  public static void injectCustomerRepository(CustomerSelectionDialog instance,
      CustomerRepository customerRepository) {
    instance.customerRepository = customerRepository;
  }
}
