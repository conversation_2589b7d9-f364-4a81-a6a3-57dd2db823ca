package com.opms.ui.business.dialog;

import com.opms.data.repository.UserRepository;
import dagger.MembersInjector;
import dagger.internal.DaggerGenerated;
import dagger.internal.InjectedFieldSignature;
import dagger.internal.QualifierMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@QualifierMetadata
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class UserSelectionDialog_MembersInjector implements MembersInjector<UserSelectionDialog> {
  private final Provider<UserRepository> userRepositoryProvider;

  public UserSelectionDialog_MembersInjector(Provider<UserRepository> userRepositoryProvider) {
    this.userRepositoryProvider = userRepositoryProvider;
  }

  public static MembersInjector<UserSelectionDialog> create(
      Provider<UserRepository> userRepositoryProvider) {
    return new UserSelectionDialog_MembersInjector(userRepositoryProvider);
  }

  @Override
  public void injectMembers(UserSelectionDialog instance) {
    injectUserRepository(instance, userRepositoryProvider.get());
  }

  @InjectedFieldSignature("com.opms.ui.business.dialog.UserSelectionDialog.userRepository")
  public static void injectUserRepository(UserSelectionDialog instance,
      UserRepository userRepository) {
    instance.userRepository = userRepository;
  }
}
