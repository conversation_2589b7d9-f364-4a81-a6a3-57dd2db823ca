<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <!-- 头像区域 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardAvatar"
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="50dp"
            app:cardElevation="4dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivAvatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop" />
        </androidx.cardview.widget.CardView>

        <!-- 用户名显示 -->
        <TextView
            android:id="@+id/tvDisplayUsername"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/text_primary"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardAvatar" />

        <!-- 个人信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardPersonalInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:contentPadding="12dp"
            app:layout_constraintTop_toBottomOf="@id/tvDisplayUsername">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="个人信息"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />


                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilName"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="姓名">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilGender"
                    style="@style/Widget.OPMS.TextInputLayout.NoDropdownIcon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="性别">

                    <AutoCompleteTextView
                        android:id="@+id/actGender"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilBirthday"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="出生年月日">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etBirthday"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilIdCard"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="身份证号码">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etIdCard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPhone"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="电话">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 工作信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardWorkInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:contentPadding="12dp"
            app:layout_constraintTop_toBottomOf="@id/cardPersonalInfo">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="工作信息"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilEmployeeId"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="工号">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etEmployeeId"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilRole"
                    style="@style/Widget.OPMS.TextInputLayout.NoDropdownIcon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="角色类型">

                    <AutoCompleteTextView
                        android:id="@+id/actRole"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilDepartment"
                    style="@style/Widget.OPMS.TextInputLayout.NoDropdownIcon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="部门">

                    <AutoCompleteTextView
                        android:id="@+id/actDepartment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPosition"
                    style="@style/Widget.OPMS.TextInputLayout.NoDropdownIcon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="职位">

                    <AutoCompleteTextView
                        android:id="@+id/actPosition"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilJob"
                    style="@style/Widget.OPMS.TextInputLayout.NoDropdownIcon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="岗位">

                    <AutoCompleteTextView
                        android:id="@+id/actJob"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPermissionTemplate"
                    style="@style/Widget.OPMS.TextInputLayout.NoDropdownIcon"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="权限模板">

                    <AutoCompleteTextView
                        android:id="@+id/actPermissionTemplate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 其他信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardOtherInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:contentPadding="12dp"
            app:layout_constraintTop_toBottomOf="@id/cardWorkInfo">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:text="其他信息"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilRemark"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="备注">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etRemark"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:inputType="textMultiLine"
                        android:lines="2" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilRegisterTime"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:hint="注册时间">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etRegisterTime"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:enabled="false" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 按钮区域 - 退出登录按钮占一整行 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnLogout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="退出登录"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardOtherInfo" />

        <!-- 编辑按钮在第二行 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnEdit"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="编辑个人信息"
            android:textColor="@color/primary"
            app:layout_constraintEnd_toStartOf="@id/btnChangePassword"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnLogout" />

        <!-- 修改密码按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnChangePassword"
            style="@style/Widget.MaterialComponents.Button.TextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="修改密码"
            android:textColor="@color/primary"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnEdit"
            app:layout_constraintTop_toBottomOf="@id/btnLogout" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
