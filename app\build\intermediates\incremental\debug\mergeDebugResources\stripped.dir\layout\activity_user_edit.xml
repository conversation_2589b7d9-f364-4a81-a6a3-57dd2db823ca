<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.UserEditActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- 用户头像 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/cardAvatar"
            android:layout_width="100dp"
            android:layout_height="100dp"
            app:cardCornerRadius="50dp"
            app:cardElevation="2dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/ivAvatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:contentDescription="用户头像"
                android:scaleType="centerCrop"
                tools:src="@drawable/ic_person" />
        </androidx.cardview.widget.CardView>

        <!-- 用户名 -->
        <TextView
            android:id="@+id/tvUsername"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardAvatar"
            tools:text="username123" />

        <!-- 基本信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardBasicInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:contentPadding="16dp"
            app:layout_constraintTop_toBottomOf="@id/tvUsername">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="基本信息"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- 姓名 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilName"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="姓名">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 性别 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilGender"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="性别">

                    <AutoCompleteTextView
                        android:id="@+id/actGender"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 出生年月日 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilBirthday"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="出生年月日">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etBirthday"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 身份证号码 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilIdCard"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="身份证号码">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etIdCard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 电话 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPhone"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="电话">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etPhone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 工作信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardWorkInfo"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="2dp"
            app:contentPadding="16dp"
            app:layout_constraintTop_toBottomOf="@id/cardBasicInfo">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="工作信息"
                    android:textColor="@color/primary"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <!-- 工号 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilEmployeeId"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="工号">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etEmployeeId"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 角色类型 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilRole"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="角色类型">

                    <AutoCompleteTextView
                        android:id="@+id/actRole"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 部门 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilDepartment"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="部门">

                    <AutoCompleteTextView
                        android:id="@+id/actDepartment"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 职位 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPosition"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="职位">

                    <AutoCompleteTextView
                        android:id="@+id/actPosition"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 岗位 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilJob"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="岗位">

                    <AutoCompleteTextView
                        android:id="@+id/actJob"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 权限模板 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilPermissionTemplate"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="权限模板">

                    <AutoCompleteTextView
                        android:id="@+id/actPermissionTemplate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="none" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 备注 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/tilRemark"
                    style="@style/Widget.OPMS.TextInputLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="备注">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/etRemark"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="top"
                        android:inputType="textMultiLine"
                        android:lines="3" />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- 注册时间 -->
        <TextView
            android:id="@+id/tvRegisterTimeLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="注册时间："
            android:textColor="@color/text_hint"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cardWorkInfo" />

        <TextView
            android:id="@+id/tvRegisterTime"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/tvRegisterTimeLabel"
            app:layout_constraintTop_toTopOf="@id/tvRegisterTimeLabel"
            tools:text="2023-01-01 12:00:00" />

        <!-- 按钮区域 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnSave"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="8dp"
            android:text="保存"
            app:layout_constraintEnd_toStartOf="@id/btnCancel"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvRegisterTime" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnCancel"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="取消"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/btnSave"
            app:layout_constraintTop_toTopOf="@id/btnSave" />

        <!-- 删除用户按钮 -->
        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnDeleteUser"
            style="@style/Widget.MaterialComponents.Button.OutlinedButton"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="删除用户"
            android:textColor="@color/error"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnSave"
            app:strokeColor="@color/error" />

        <!-- 进度条 -->
        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
