package com.opms;

import android.app.Activity;
import android.app.Service;
import android.view.View;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.SavedStateHandle;
import androidx.lifecycle.ViewModel;
import com.google.errorprone.annotations.CanIgnoreReturnValue;
import com.opms.data.local.AppDatabase;
import com.opms.data.local.PreferencesManager;
import com.opms.data.local.dao.CustomerDao;
import com.opms.data.local.dao.DepartmentDao;
import com.opms.data.local.dao.OrderDao;
import com.opms.data.local.dao.PositionDao;
import com.opms.data.local.dao.PostDao;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.remote.ApiService;
import com.opms.data.remote.AuthInterceptor;
import com.opms.data.remote.interceptor.ResponseInterceptor;
import com.opms.data.repository.ComponentRepositoryImpl;
import com.opms.data.repository.CustomerRepository;
import com.opms.data.repository.CustomerRepositoryImpl;
import com.opms.data.repository.DepartmentRepository;
import com.opms.data.repository.DepartmentRepositoryImpl;
import com.opms.data.repository.ImageUploadRepository;
import com.opms.data.repository.ImageUploadRepositoryImpl;
import com.opms.data.repository.OrderRepository;
import com.opms.data.repository.OrderRepositoryImpl;
import com.opms.data.repository.PermissionRepositoryImpl;
import com.opms.data.repository.PositionRepository;
import com.opms.data.repository.PositionRepositoryImpl;
import com.opms.data.repository.PostRepository;
import com.opms.data.repository.PostRepositoryImpl;
import com.opms.data.repository.ProcessPostMappingRepositoryImpl;
import com.opms.data.repository.ProcessTemplateRepositoryImpl;
import com.opms.data.repository.ProductRepository;
import com.opms.data.repository.ProductRepositoryImpl;
import com.opms.data.repository.UserAuditRepositoryImpl;
import com.opms.data.repository.UserRepositoryImpl;
import com.opms.di.module.AppModule;
import com.opms.di.module.AppModule_ProvideAppDatabaseFactory;
import com.opms.di.module.AppModule_ProvideCustomerDaoFactory;
import com.opms.di.module.AppModule_ProvideDepartmentDaoFactory;
import com.opms.di.module.AppModule_ProvideOrderDaoFactory;
import com.opms.di.module.AppModule_ProvidePositionDaoFactory;
import com.opms.di.module.AppModule_ProvidePostDaoFactory;
import com.opms.di.module.AppModule_ProvidePreferencesManagerFactory;
import com.opms.di.module.NetworkModule;
import com.opms.di.module.NetworkModule_ProvideApiServiceFactory;
import com.opms.di.module.NetworkModule_ProvideAuthInterceptorFactory;
import com.opms.di.module.NetworkModule_ProvideLoggingInterceptorFactory;
import com.opms.di.module.NetworkModule_ProvideOkHttpClientFactory;
import com.opms.di.module.NetworkModule_ProvideResponseConverterFactory;
import com.opms.di.module.NetworkModule_ProvideResponseInterceptorFactory;
import com.opms.di.module.NetworkModule_ProvideRetrofitFactory;
import com.opms.ui.audit.UserAuditFragment;
import com.opms.ui.business.BusinessFragment;
import com.opms.ui.business.ComponentEditActivity;
import com.opms.ui.business.ComponentEditActivity_MembersInjector;
import com.opms.ui.business.ComponentManagementActivity;
import com.opms.ui.business.ComponentManagementActivity_MembersInjector;
import com.opms.ui.business.CustomerEditActivity;
import com.opms.ui.business.CustomerEditActivity_MembersInjector;
import com.opms.ui.business.CustomerManagementActivity;
import com.opms.ui.business.CustomerManagementActivity_MembersInjector;
import com.opms.ui.business.OrderEntryActivity;
import com.opms.ui.business.OrderEntryActivity_MembersInjector;
import com.opms.ui.business.OrderManagementActivity;
import com.opms.ui.business.ProductEditActivity;
import com.opms.ui.business.ProductEditActivity_MembersInjector;
import com.opms.ui.business.ProductManagementActivity;
import com.opms.ui.business.ProductManagementActivity_MembersInjector;
import com.opms.ui.business.dialog.ComponentSelectionDialog;
import com.opms.ui.business.dialog.ComponentSelectionDialog_MembersInjector;
import com.opms.ui.business.dialog.CustomerSelectionDialog;
import com.opms.ui.business.dialog.CustomerSelectionDialog_MembersInjector;
import com.opms.ui.business.dialog.ProductSelectionDialog;
import com.opms.ui.business.dialog.ProductSelectionDialog_MembersInjector;
import com.opms.ui.business.dialog.UserSelectionDialog;
import com.opms.ui.business.dialog.UserSelectionDialog_MembersInjector;
import com.opms.ui.login.LoginActivity;
import com.opms.ui.login.LoginActivity_MembersInjector;
import com.opms.ui.profile.AvatarViewActivity;
import com.opms.ui.profile.AvatarViewActivity_MembersInjector;
import com.opms.ui.profile.PasswordChangeActivity;
import com.opms.ui.profile.PasswordChangeActivity_MembersInjector;
import com.opms.ui.profile.ProfileActivity;
import com.opms.ui.profile.ProfileActivity_MembersInjector;
import com.opms.ui.profile.ProfileEditActivity;
import com.opms.ui.profile.ProfileEditActivity_MembersInjector;
import com.opms.ui.profile.ProfileFragment;
import com.opms.ui.profile.ProfileFragment_MembersInjector;
import com.opms.ui.register.RegisterActivity;
import com.opms.ui.register.RegisterActivity_MembersInjector;
import com.opms.ui.splash.SplashActivity;
import com.opms.ui.splash.SplashActivity_MembersInjector;
import com.opms.ui.system.DepartmentManagementActivity;
import com.opms.ui.system.DepartmentManagementActivity_MembersInjector;
import com.opms.ui.system.PermissionEditActivity;
import com.opms.ui.system.PermissionEditActivity_MembersInjector;
import com.opms.ui.system.PermissionManagementActivity;
import com.opms.ui.system.PermissionManagementActivity_MembersInjector;
import com.opms.ui.system.PositionEditActivity;
import com.opms.ui.system.PositionEditActivity_MembersInjector;
import com.opms.ui.system.PositionManagementActivity;
import com.opms.ui.system.PositionManagementActivity_MembersInjector;
import com.opms.ui.system.PostEditActivity;
import com.opms.ui.system.PostEditActivity_MembersInjector;
import com.opms.ui.system.PostManagementActivity;
import com.opms.ui.system.PostManagementActivity_MembersInjector;
import com.opms.ui.system.ProcessPostMappingEditActivity;
import com.opms.ui.system.ProcessPostMappingEditActivity_MembersInjector;
import com.opms.ui.system.ProcessPostMappingManagementActivity;
import com.opms.ui.system.ProcessPostMappingManagementActivity_MembersInjector;
import com.opms.ui.system.ProcessTemplateEditActivity;
import com.opms.ui.system.ProcessTemplateEditActivity_MembersInjector;
import com.opms.ui.system.ProcessTemplateManagementActivity;
import com.opms.ui.system.ProcessTemplateManagementActivity_MembersInjector;
import com.opms.ui.system.SystemManageFragment;
import com.opms.ui.system.UserAuditActivity;
import com.opms.ui.system.UserAuditActivity_MembersInjector;
import com.opms.ui.system.UserAuditListActivity;
import com.opms.ui.system.UserAuditListActivity_MembersInjector;
import com.opms.ui.system.UserEditActivity;
import com.opms.ui.system.UserEditActivity_MembersInjector;
import com.opms.ui.system.UserManagementActivity;
import com.opms.ui.system.UserManagementActivity_MembersInjector;
import com.opms.ui.todo.TodoFragment;
import com.opms.ui.user.UserAuditFragment_MembersInjector;
import dagger.hilt.android.ActivityRetainedLifecycle;
import dagger.hilt.android.ViewModelLifecycle;
import dagger.hilt.android.flags.HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule;
import dagger.hilt.android.internal.builders.ActivityComponentBuilder;
import dagger.hilt.android.internal.builders.ActivityRetainedComponentBuilder;
import dagger.hilt.android.internal.builders.FragmentComponentBuilder;
import dagger.hilt.android.internal.builders.ServiceComponentBuilder;
import dagger.hilt.android.internal.builders.ViewComponentBuilder;
import dagger.hilt.android.internal.builders.ViewModelComponentBuilder;
import dagger.hilt.android.internal.builders.ViewWithFragmentComponentBuilder;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories;
import dagger.hilt.android.internal.lifecycle.DefaultViewModelFactories_InternalFactoryFactory_Factory;
import dagger.hilt.android.internal.managers.ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory;
import dagger.hilt.android.internal.modules.ApplicationContextModule;
import dagger.hilt.android.internal.modules.ApplicationContextModule_ProvideContextFactory;
import dagger.internal.DaggerGenerated;
import dagger.internal.DoubleCheck;
import dagger.internal.Preconditions;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import javax.annotation.processing.Generated;
import javax.inject.Provider;
import okhttp3.OkHttpClient;
import okhttp3.ResponseBody;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Converter;
import retrofit2.Retrofit;

@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class DaggerOPMSApplication_HiltComponents_SingletonC {
  private DaggerOPMSApplication_HiltComponents_SingletonC() {
  }

  public static Builder builder() {
    return new Builder();
  }

  public static final class Builder {
    private AppModule appModule;

    private ApplicationContextModule applicationContextModule;

    private NetworkModule networkModule;

    private Builder() {
    }

    public Builder appModule(AppModule appModule) {
      this.appModule = Preconditions.checkNotNull(appModule);
      return this;
    }

    public Builder applicationContextModule(ApplicationContextModule applicationContextModule) {
      this.applicationContextModule = Preconditions.checkNotNull(applicationContextModule);
      return this;
    }

    /**
     * @deprecated This module is declared, but an instance is not used in the component. This method is a no-op. For more, see https://dagger.dev/unused-modules.
     */
    @Deprecated
    public Builder hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule(
        HiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule) {
      Preconditions.checkNotNull(hiltWrapper_FragmentGetContextFix_FragmentGetContextFixModule);
      return this;
    }

    public Builder networkModule(NetworkModule networkModule) {
      this.networkModule = Preconditions.checkNotNull(networkModule);
      return this;
    }

    public OPMSApplication_HiltComponents.SingletonC build() {
      if (appModule == null) {
        this.appModule = new AppModule();
      }
      Preconditions.checkBuilderRequirement(applicationContextModule, ApplicationContextModule.class);
      if (networkModule == null) {
        this.networkModule = new NetworkModule();
      }
      return new SingletonCImpl(appModule, applicationContextModule, networkModule);
    }
  }

  private static final class ActivityRetainedCBuilder implements OPMSApplication_HiltComponents.ActivityRetainedC.Builder {
    private final SingletonCImpl singletonCImpl;

    private ActivityRetainedCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public OPMSApplication_HiltComponents.ActivityRetainedC build() {
      return new ActivityRetainedCImpl(singletonCImpl);
    }
  }

  private static final class ActivityCBuilder implements OPMSApplication_HiltComponents.ActivityC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private Activity activity;

    private ActivityCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ActivityCBuilder activity(Activity activity) {
      this.activity = Preconditions.checkNotNull(activity);
      return this;
    }

    @Override
    public OPMSApplication_HiltComponents.ActivityC build() {
      Preconditions.checkBuilderRequirement(activity, Activity.class);
      return new ActivityCImpl(singletonCImpl, activityRetainedCImpl, activity);
    }
  }

  private static final class FragmentCBuilder implements OPMSApplication_HiltComponents.FragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private Fragment fragment;

    private FragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public FragmentCBuilder fragment(Fragment fragment) {
      this.fragment = Preconditions.checkNotNull(fragment);
      return this;
    }

    @Override
    public OPMSApplication_HiltComponents.FragmentC build() {
      Preconditions.checkBuilderRequirement(fragment, Fragment.class);
      return new FragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragment);
    }
  }

  private static final class ViewWithFragmentCBuilder implements OPMSApplication_HiltComponents.ViewWithFragmentC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private View view;

    private ViewWithFragmentCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;
    }

    @Override
    public ViewWithFragmentCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public OPMSApplication_HiltComponents.ViewWithFragmentC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewWithFragmentCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl, view);
    }
  }

  private static final class ViewCBuilder implements OPMSApplication_HiltComponents.ViewC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private View view;

    private ViewCBuilder(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
    }

    @Override
    public ViewCBuilder view(View view) {
      this.view = Preconditions.checkNotNull(view);
      return this;
    }

    @Override
    public OPMSApplication_HiltComponents.ViewC build() {
      Preconditions.checkBuilderRequirement(view, View.class);
      return new ViewCImpl(singletonCImpl, activityRetainedCImpl, activityCImpl, view);
    }
  }

  private static final class ViewModelCBuilder implements OPMSApplication_HiltComponents.ViewModelC.Builder {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private SavedStateHandle savedStateHandle;

    private ViewModelLifecycle viewModelLifecycle;

    private ViewModelCBuilder(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
    }

    @Override
    public ViewModelCBuilder savedStateHandle(SavedStateHandle handle) {
      this.savedStateHandle = Preconditions.checkNotNull(handle);
      return this;
    }

    @Override
    public ViewModelCBuilder viewModelLifecycle(ViewModelLifecycle viewModelLifecycle) {
      this.viewModelLifecycle = Preconditions.checkNotNull(viewModelLifecycle);
      return this;
    }

    @Override
    public OPMSApplication_HiltComponents.ViewModelC build() {
      Preconditions.checkBuilderRequirement(savedStateHandle, SavedStateHandle.class);
      Preconditions.checkBuilderRequirement(viewModelLifecycle, ViewModelLifecycle.class);
      return new ViewModelCImpl(singletonCImpl, activityRetainedCImpl, savedStateHandle, viewModelLifecycle);
    }
  }

  private static final class ServiceCBuilder implements OPMSApplication_HiltComponents.ServiceC.Builder {
    private final SingletonCImpl singletonCImpl;

    private Service service;

    private ServiceCBuilder(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;
    }

    @Override
    public ServiceCBuilder service(Service service) {
      this.service = Preconditions.checkNotNull(service);
      return this;
    }

    @Override
    public OPMSApplication_HiltComponents.ServiceC build() {
      Preconditions.checkBuilderRequirement(service, Service.class);
      return new ServiceCImpl(singletonCImpl, service);
    }
  }

  private static final class ViewWithFragmentCImpl extends OPMSApplication_HiltComponents.ViewWithFragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl;

    private final ViewWithFragmentCImpl viewWithFragmentCImpl = this;

    private ViewWithFragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        FragmentCImpl fragmentCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;
      this.fragmentCImpl = fragmentCImpl;


    }
  }

  private static final class FragmentCImpl extends OPMSApplication_HiltComponents.FragmentC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final FragmentCImpl fragmentCImpl = this;

    private FragmentCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, ActivityCImpl activityCImpl,
        Fragment fragmentParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }

    @Override
    public void injectUserAuditFragment(UserAuditFragment userAuditFragment) {
    }

    @Override
    public void injectUserAuditFragment(com.opms.ui.user.UserAuditFragment userAuditFragment) {
      injectUserAuditFragment2(userAuditFragment);
    }

    @Override
    public void injectBusinessFragment(BusinessFragment businessFragment) {
    }

    @Override
    public void injectComponentSelectionDialog(ComponentSelectionDialog componentSelectionDialog) {
      injectComponentSelectionDialog2(componentSelectionDialog);
    }

    @Override
    public void injectCustomerSelectionDialog(CustomerSelectionDialog customerSelectionDialog) {
      injectCustomerSelectionDialog2(customerSelectionDialog);
    }

    @Override
    public void injectProductSelectionDialog(ProductSelectionDialog productSelectionDialog) {
      injectProductSelectionDialog2(productSelectionDialog);
    }

    @Override
    public void injectUserSelectionDialog(UserSelectionDialog userSelectionDialog) {
      injectUserSelectionDialog2(userSelectionDialog);
    }

    @Override
    public void injectProfileFragment(ProfileFragment profileFragment) {
      injectProfileFragment2(profileFragment);
    }

    @Override
    public void injectSystemManageFragment(SystemManageFragment systemManageFragment) {
    }

    @Override
    public void injectTodoFragment(TodoFragment todoFragment) {
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return activityCImpl.getHiltInternalFactoryFactory();
    }

    @Override
    public ViewWithFragmentComponentBuilder viewWithFragmentComponentBuilder() {
      return new ViewWithFragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl, fragmentCImpl);
    }

    @CanIgnoreReturnValue
    private com.opms.ui.user.UserAuditFragment injectUserAuditFragment2(
        com.opms.ui.user.UserAuditFragment instance) {
      UserAuditFragment_MembersInjector.injectUserAuditRepository(instance, singletonCImpl.userAuditRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ComponentSelectionDialog injectComponentSelectionDialog2(
        ComponentSelectionDialog instance) {
      ComponentSelectionDialog_MembersInjector.injectComponentRepository(instance, singletonCImpl.componentRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private CustomerSelectionDialog injectCustomerSelectionDialog2(
        CustomerSelectionDialog instance) {
      CustomerSelectionDialog_MembersInjector.injectCustomerRepository(instance, singletonCImpl.bindCustomerRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProductSelectionDialog injectProductSelectionDialog2(ProductSelectionDialog instance) {
      ProductSelectionDialog_MembersInjector.injectProductRepository(instance, singletonCImpl.bindProductRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private UserSelectionDialog injectUserSelectionDialog2(UserSelectionDialog instance) {
      UserSelectionDialog_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProfileFragment injectProfileFragment2(ProfileFragment instance) {
      ProfileFragment_MembersInjector.injectPreferencesManager(instance, singletonCImpl.providePreferencesManagerProvider.get());
      ProfileFragment_MembersInjector.injectApiService(instance, singletonCImpl.provideApiServiceProvider.get());
      ProfileFragment_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      return instance;
    }
  }

  private static final class ViewCImpl extends OPMSApplication_HiltComponents.ViewC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl;

    private final ViewCImpl viewCImpl = this;

    private ViewCImpl(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
        ActivityCImpl activityCImpl, View viewParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;
      this.activityCImpl = activityCImpl;


    }
  }

  private static final class ActivityCImpl extends OPMSApplication_HiltComponents.ActivityC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ActivityCImpl activityCImpl = this;

    private ActivityCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, Activity activityParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public void injectMainActivity(MainActivity mainActivity) {
      injectMainActivity2(mainActivity);
    }

    @Override
    public void injectComponentEditActivity(ComponentEditActivity componentEditActivity) {
      injectComponentEditActivity2(componentEditActivity);
    }

    @Override
    public void injectComponentManagementActivity(
        ComponentManagementActivity componentManagementActivity) {
      injectComponentManagementActivity2(componentManagementActivity);
    }

    @Override
    public void injectCustomerEditActivity(CustomerEditActivity customerEditActivity) {
      injectCustomerEditActivity2(customerEditActivity);
    }

    @Override
    public void injectCustomerManagementActivity(
        CustomerManagementActivity customerManagementActivity) {
      injectCustomerManagementActivity2(customerManagementActivity);
    }

    @Override
    public void injectOrderEntryActivity(OrderEntryActivity orderEntryActivity) {
      injectOrderEntryActivity2(orderEntryActivity);
    }

    @Override
    public void injectOrderManagementActivity(OrderManagementActivity orderManagementActivity) {
    }

    @Override
    public void injectProductEditActivity(ProductEditActivity productEditActivity) {
      injectProductEditActivity2(productEditActivity);
    }

    @Override
    public void injectProductManagementActivity(
        ProductManagementActivity productManagementActivity) {
      injectProductManagementActivity2(productManagementActivity);
    }

    @Override
    public void injectLoginActivity(LoginActivity loginActivity) {
      injectLoginActivity2(loginActivity);
    }

    @Override
    public void injectAvatarViewActivity(AvatarViewActivity avatarViewActivity) {
      injectAvatarViewActivity2(avatarViewActivity);
    }

    @Override
    public void injectPasswordChangeActivity(PasswordChangeActivity passwordChangeActivity) {
      injectPasswordChangeActivity2(passwordChangeActivity);
    }

    @Override
    public void injectProfileActivity(ProfileActivity profileActivity) {
      injectProfileActivity2(profileActivity);
    }

    @Override
    public void injectProfileEditActivity(ProfileEditActivity profileEditActivity) {
      injectProfileEditActivity2(profileEditActivity);
    }

    @Override
    public void injectRegisterActivity(RegisterActivity registerActivity) {
      injectRegisterActivity2(registerActivity);
    }

    @Override
    public void injectSplashActivity(SplashActivity splashActivity) {
      injectSplashActivity2(splashActivity);
    }

    @Override
    public void injectDepartmentManagementActivity(
        DepartmentManagementActivity departmentManagementActivity) {
      injectDepartmentManagementActivity2(departmentManagementActivity);
    }

    @Override
    public void injectPermissionEditActivity(PermissionEditActivity permissionEditActivity) {
      injectPermissionEditActivity2(permissionEditActivity);
    }

    @Override
    public void injectPermissionManagementActivity(
        PermissionManagementActivity permissionManagementActivity) {
      injectPermissionManagementActivity2(permissionManagementActivity);
    }

    @Override
    public void injectPositionEditActivity(PositionEditActivity positionEditActivity) {
      injectPositionEditActivity2(positionEditActivity);
    }

    @Override
    public void injectPositionManagementActivity(
        PositionManagementActivity positionManagementActivity) {
      injectPositionManagementActivity2(positionManagementActivity);
    }

    @Override
    public void injectPostEditActivity(PostEditActivity postEditActivity) {
      injectPostEditActivity2(postEditActivity);
    }

    @Override
    public void injectPostManagementActivity(PostManagementActivity postManagementActivity) {
      injectPostManagementActivity2(postManagementActivity);
    }

    @Override
    public void injectProcessPostMappingEditActivity(
        ProcessPostMappingEditActivity processPostMappingEditActivity) {
      injectProcessPostMappingEditActivity2(processPostMappingEditActivity);
    }

    @Override
    public void injectProcessPostMappingManagementActivity(
        ProcessPostMappingManagementActivity processPostMappingManagementActivity) {
      injectProcessPostMappingManagementActivity2(processPostMappingManagementActivity);
    }

    @Override
    public void injectProcessTemplateEditActivity(
        ProcessTemplateEditActivity processTemplateEditActivity) {
      injectProcessTemplateEditActivity2(processTemplateEditActivity);
    }

    @Override
    public void injectProcessTemplateManagementActivity(
        ProcessTemplateManagementActivity processTemplateManagementActivity) {
      injectProcessTemplateManagementActivity2(processTemplateManagementActivity);
    }

    @Override
    public void injectUserAuditActivity(UserAuditActivity userAuditActivity) {
      injectUserAuditActivity2(userAuditActivity);
    }

    @Override
    public void injectUserAuditListActivity(UserAuditListActivity userAuditListActivity) {
      injectUserAuditListActivity2(userAuditListActivity);
    }

    @Override
    public void injectUserEditActivity(UserEditActivity userEditActivity) {
      injectUserEditActivity2(userEditActivity);
    }

    @Override
    public void injectUserManagementActivity(UserManagementActivity userManagementActivity) {
      injectUserManagementActivity2(userManagementActivity);
    }

    @Override
    public DefaultViewModelFactories.InternalFactoryFactory getHiltInternalFactoryFactory() {
      return DefaultViewModelFactories_InternalFactoryFactory_Factory.newInstance(Collections.<String>emptySet(), new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl));
    }

    @Override
    public Set<String> getViewModelKeys() {
      return Collections.<String>emptySet();
    }

    @Override
    public ViewModelComponentBuilder getViewModelComponentBuilder() {
      return new ViewModelCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public FragmentComponentBuilder fragmentComponentBuilder() {
      return new FragmentCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @Override
    public ViewComponentBuilder viewComponentBuilder() {
      return new ViewCBuilder(singletonCImpl, activityRetainedCImpl, activityCImpl);
    }

    @CanIgnoreReturnValue
    private MainActivity injectMainActivity2(MainActivity instance) {
      MainActivity_MembersInjector.injectPreferencesManager(instance, singletonCImpl.providePreferencesManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ComponentEditActivity injectComponentEditActivity2(ComponentEditActivity instance) {
      ComponentEditActivity_MembersInjector.injectComponentRepository(instance, singletonCImpl.componentRepositoryImplProvider.get());
      ComponentEditActivity_MembersInjector.injectImageUploadRepository(instance, singletonCImpl.bindImageUploadRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ComponentManagementActivity injectComponentManagementActivity2(
        ComponentManagementActivity instance) {
      ComponentManagementActivity_MembersInjector.injectComponentRepository(instance, singletonCImpl.componentRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private CustomerEditActivity injectCustomerEditActivity2(CustomerEditActivity instance) {
      CustomerEditActivity_MembersInjector.injectCustomerRepository(instance, singletonCImpl.bindCustomerRepositoryProvider.get());
      CustomerEditActivity_MembersInjector.injectImageUploadRepository(instance, singletonCImpl.bindImageUploadRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private CustomerManagementActivity injectCustomerManagementActivity2(
        CustomerManagementActivity instance) {
      CustomerManagementActivity_MembersInjector.injectCustomerRepository(instance, singletonCImpl.bindCustomerRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private OrderEntryActivity injectOrderEntryActivity2(OrderEntryActivity instance) {
      OrderEntryActivity_MembersInjector.injectOrderRepository(instance, singletonCImpl.bindOrderRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProductEditActivity injectProductEditActivity2(ProductEditActivity instance) {
      ProductEditActivity_MembersInjector.injectProductRepository(instance, singletonCImpl.bindProductRepositoryProvider.get());
      ProductEditActivity_MembersInjector.injectImageUploadRepository(instance, singletonCImpl.bindImageUploadRepositoryProvider.get());
      ProductEditActivity_MembersInjector.injectComponentRepository(instance, singletonCImpl.componentRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProductManagementActivity injectProductManagementActivity2(
        ProductManagementActivity instance) {
      ProductManagementActivity_MembersInjector.injectProductRepository(instance, singletonCImpl.bindProductRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private LoginActivity injectLoginActivity2(LoginActivity instance) {
      LoginActivity_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      LoginActivity_MembersInjector.injectPreferencesManager(instance, singletonCImpl.providePreferencesManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private AvatarViewActivity injectAvatarViewActivity2(AvatarViewActivity instance) {
      AvatarViewActivity_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      AvatarViewActivity_MembersInjector.injectImageUploadRepository(instance, singletonCImpl.bindImageUploadRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PasswordChangeActivity injectPasswordChangeActivity2(PasswordChangeActivity instance) {
      PasswordChangeActivity_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProfileActivity injectProfileActivity2(ProfileActivity instance) {
      ProfileActivity_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProfileEditActivity injectProfileEditActivity2(ProfileEditActivity instance) {
      ProfileEditActivity_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private RegisterActivity injectRegisterActivity2(RegisterActivity instance) {
      RegisterActivity_MembersInjector.injectUserRepository(instance, singletonCImpl.userRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private SplashActivity injectSplashActivity2(SplashActivity instance) {
      SplashActivity_MembersInjector.injectPreferencesManager(instance, singletonCImpl.providePreferencesManagerProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private DepartmentManagementActivity injectDepartmentManagementActivity2(
        DepartmentManagementActivity instance) {
      DepartmentManagementActivity_MembersInjector.injectDepartmentRepository(instance, singletonCImpl.bindDepartmentRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PermissionEditActivity injectPermissionEditActivity2(PermissionEditActivity instance) {
      PermissionEditActivity_MembersInjector.injectPermissionRepository(instance, singletonCImpl.permissionRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PermissionManagementActivity injectPermissionManagementActivity2(
        PermissionManagementActivity instance) {
      PermissionManagementActivity_MembersInjector.injectPermissionRepository(instance, singletonCImpl.permissionRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PositionEditActivity injectPositionEditActivity2(PositionEditActivity instance) {
      PositionEditActivity_MembersInjector.injectPositionRepository(instance, singletonCImpl.bindPositionRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PositionManagementActivity injectPositionManagementActivity2(
        PositionManagementActivity instance) {
      PositionManagementActivity_MembersInjector.injectPositionRepository(instance, singletonCImpl.bindPositionRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PostEditActivity injectPostEditActivity2(PostEditActivity instance) {
      PostEditActivity_MembersInjector.injectPostRepository(instance, singletonCImpl.bindPostRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private PostManagementActivity injectPostManagementActivity2(PostManagementActivity instance) {
      PostManagementActivity_MembersInjector.injectPostRepository(instance, singletonCImpl.bindPostRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProcessPostMappingEditActivity injectProcessPostMappingEditActivity2(
        ProcessPostMappingEditActivity instance) {
      ProcessPostMappingEditActivity_MembersInjector.injectProcessPostMappingRepository(instance, singletonCImpl.processPostMappingRepositoryImplProvider.get());
      ProcessPostMappingEditActivity_MembersInjector.injectProcessTemplateRepository(instance, singletonCImpl.processTemplateRepositoryImplProvider.get());
      ProcessPostMappingEditActivity_MembersInjector.injectPostRepository(instance, singletonCImpl.bindPostRepositoryProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProcessPostMappingManagementActivity injectProcessPostMappingManagementActivity2(
        ProcessPostMappingManagementActivity instance) {
      ProcessPostMappingManagementActivity_MembersInjector.injectProcessPostMappingRepository(instance, singletonCImpl.processPostMappingRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProcessTemplateEditActivity injectProcessTemplateEditActivity2(
        ProcessTemplateEditActivity instance) {
      ProcessTemplateEditActivity_MembersInjector.injectProcessTemplateRepository(instance, singletonCImpl.processTemplateRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private ProcessTemplateManagementActivity injectProcessTemplateManagementActivity2(
        ProcessTemplateManagementActivity instance) {
      ProcessTemplateManagementActivity_MembersInjector.injectProcessTemplateRepository(instance, singletonCImpl.processTemplateRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private UserAuditActivity injectUserAuditActivity2(UserAuditActivity instance) {
      UserAuditActivity_MembersInjector.injectUserAuditRepository(instance, singletonCImpl.userAuditRepositoryImplProvider.get());
      UserAuditActivity_MembersInjector.injectApiService(instance, singletonCImpl.provideApiServiceProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private UserAuditListActivity injectUserAuditListActivity2(UserAuditListActivity instance) {
      UserAuditListActivity_MembersInjector.injectUserAuditRepository(instance, singletonCImpl.userAuditRepositoryImplProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private UserEditActivity injectUserEditActivity2(UserEditActivity instance) {
      UserEditActivity_MembersInjector.injectApiService(instance, singletonCImpl.provideApiServiceProvider.get());
      return instance;
    }

    @CanIgnoreReturnValue
    private UserManagementActivity injectUserManagementActivity2(UserManagementActivity instance) {
      UserManagementActivity_MembersInjector.injectApiService(instance, singletonCImpl.provideApiServiceProvider.get());
      return instance;
    }
  }

  private static final class ViewModelCImpl extends OPMSApplication_HiltComponents.ViewModelC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl;

    private final ViewModelCImpl viewModelCImpl = this;

    private ViewModelCImpl(SingletonCImpl singletonCImpl,
        ActivityRetainedCImpl activityRetainedCImpl, SavedStateHandle savedStateHandleParam,
        ViewModelLifecycle viewModelLifecycleParam) {
      this.singletonCImpl = singletonCImpl;
      this.activityRetainedCImpl = activityRetainedCImpl;


    }

    @Override
    public Map<String, Provider<ViewModel>> getHiltViewModelMap() {
      return Collections.<String, Provider<ViewModel>>emptyMap();
    }
  }

  private static final class ActivityRetainedCImpl extends OPMSApplication_HiltComponents.ActivityRetainedC {
    private final SingletonCImpl singletonCImpl;

    private final ActivityRetainedCImpl activityRetainedCImpl = this;

    private Provider<ActivityRetainedLifecycle> provideActivityRetainedLifecycleProvider;

    private ActivityRetainedCImpl(SingletonCImpl singletonCImpl) {
      this.singletonCImpl = singletonCImpl;

      initialize();

    }

    @SuppressWarnings("unchecked")
    private void initialize() {
      this.provideActivityRetainedLifecycleProvider = DoubleCheck.provider(new SwitchingProvider<ActivityRetainedLifecycle>(singletonCImpl, activityRetainedCImpl, 0));
    }

    @Override
    public ActivityComponentBuilder activityComponentBuilder() {
      return new ActivityCBuilder(singletonCImpl, activityRetainedCImpl);
    }

    @Override
    public ActivityRetainedLifecycle getActivityRetainedLifecycle() {
      return provideActivityRetainedLifecycleProvider.get();
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final ActivityRetainedCImpl activityRetainedCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, ActivityRetainedCImpl activityRetainedCImpl,
          int id) {
        this.singletonCImpl = singletonCImpl;
        this.activityRetainedCImpl = activityRetainedCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // dagger.hilt.android.ActivityRetainedLifecycle 
          return (T) ActivityRetainedComponentManager_LifecycleModule_ProvideActivityRetainedLifecycleFactory.provideActivityRetainedLifecycle();

          default: throw new AssertionError(id);
        }
      }
    }
  }

  private static final class ServiceCImpl extends OPMSApplication_HiltComponents.ServiceC {
    private final SingletonCImpl singletonCImpl;

    private final ServiceCImpl serviceCImpl = this;

    private ServiceCImpl(SingletonCImpl singletonCImpl, Service serviceParam) {
      this.singletonCImpl = singletonCImpl;


    }
  }

  private static final class SingletonCImpl extends OPMSApplication_HiltComponents.SingletonC {
    private final NetworkModule networkModule;

    private final ApplicationContextModule applicationContextModule;

    private final AppModule appModule;

    private final SingletonCImpl singletonCImpl = this;

    private Provider<HttpLoggingInterceptor> provideLoggingInterceptorProvider;

    private Provider<PreferencesManager> providePreferencesManagerProvider;

    private Provider<AuthInterceptor> provideAuthInterceptorProvider;

    private Provider<Converter<ResponseBody, ApiResponse<?>>> provideResponseConverterProvider;

    private Provider<ResponseInterceptor> provideResponseInterceptorProvider;

    private Provider<OkHttpClient> provideOkHttpClientProvider;

    private Provider<Retrofit> provideRetrofitProvider;

    private Provider<ApiService> provideApiServiceProvider;

    private Provider<UserRepositoryImpl> userRepositoryImplProvider;

    private Provider<ComponentRepositoryImpl> componentRepositoryImplProvider;

    private Provider<ImageUploadRepositoryImpl> imageUploadRepositoryImplProvider;

    private Provider<ImageUploadRepository> bindImageUploadRepositoryProvider;

    private Provider<AppDatabase> provideAppDatabaseProvider;

    private Provider<CustomerDao> provideCustomerDaoProvider;

    private Provider<CustomerRepositoryImpl> customerRepositoryImplProvider;

    private Provider<CustomerRepository> bindCustomerRepositoryProvider;

    private Provider<OrderDao> provideOrderDaoProvider;

    private Provider<OrderRepositoryImpl> orderRepositoryImplProvider;

    private Provider<OrderRepository> bindOrderRepositoryProvider;

    private Provider<ProductRepositoryImpl> productRepositoryImplProvider;

    private Provider<ProductRepository> bindProductRepositoryProvider;

    private Provider<DepartmentDao> provideDepartmentDaoProvider;

    private Provider<DepartmentRepositoryImpl> departmentRepositoryImplProvider;

    private Provider<DepartmentRepository> bindDepartmentRepositoryProvider;

    private Provider<PermissionRepositoryImpl> permissionRepositoryImplProvider;

    private Provider<PositionDao> providePositionDaoProvider;

    private Provider<PositionRepositoryImpl> positionRepositoryImplProvider;

    private Provider<PositionRepository> bindPositionRepositoryProvider;

    private Provider<PostDao> providePostDaoProvider;

    private Provider<PostRepositoryImpl> postRepositoryImplProvider;

    private Provider<PostRepository> bindPostRepositoryProvider;

    private Provider<ProcessPostMappingRepositoryImpl> processPostMappingRepositoryImplProvider;

    private Provider<ProcessTemplateRepositoryImpl> processTemplateRepositoryImplProvider;

    private Provider<UserAuditRepositoryImpl> userAuditRepositoryImplProvider;

    private SingletonCImpl(AppModule appModuleParam,
        ApplicationContextModule applicationContextModuleParam, NetworkModule networkModuleParam) {
      this.networkModule = networkModuleParam;
      this.applicationContextModule = applicationContextModuleParam;
      this.appModule = appModuleParam;
      initialize(appModuleParam, applicationContextModuleParam, networkModuleParam);

    }

    @SuppressWarnings("unchecked")
    private void initialize(final AppModule appModuleParam,
        final ApplicationContextModule applicationContextModuleParam,
        final NetworkModule networkModuleParam) {
      this.provideLoggingInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<HttpLoggingInterceptor>(singletonCImpl, 4));
      this.providePreferencesManagerProvider = DoubleCheck.provider(new SwitchingProvider<PreferencesManager>(singletonCImpl, 6));
      this.provideAuthInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<AuthInterceptor>(singletonCImpl, 5));
      this.provideResponseConverterProvider = DoubleCheck.provider(new SwitchingProvider<Converter<ResponseBody, ApiResponse<?>>>(singletonCImpl, 8));
      this.provideResponseInterceptorProvider = DoubleCheck.provider(new SwitchingProvider<ResponseInterceptor>(singletonCImpl, 7));
      this.provideOkHttpClientProvider = DoubleCheck.provider(new SwitchingProvider<OkHttpClient>(singletonCImpl, 3));
      this.provideRetrofitProvider = DoubleCheck.provider(new SwitchingProvider<Retrofit>(singletonCImpl, 2));
      this.provideApiServiceProvider = DoubleCheck.provider(new SwitchingProvider<ApiService>(singletonCImpl, 1));
      this.userRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserRepositoryImpl>(singletonCImpl, 0));
      this.componentRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<ComponentRepositoryImpl>(singletonCImpl, 9));
      this.imageUploadRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 10);
      this.bindImageUploadRepositoryProvider = DoubleCheck.provider((Provider) imageUploadRepositoryImplProvider);
      this.provideAppDatabaseProvider = DoubleCheck.provider(new SwitchingProvider<AppDatabase>(singletonCImpl, 13));
      this.provideCustomerDaoProvider = DoubleCheck.provider(new SwitchingProvider<CustomerDao>(singletonCImpl, 12));
      this.customerRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 11);
      this.bindCustomerRepositoryProvider = DoubleCheck.provider((Provider) customerRepositoryImplProvider);
      this.provideOrderDaoProvider = DoubleCheck.provider(new SwitchingProvider<OrderDao>(singletonCImpl, 15));
      this.orderRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 14);
      this.bindOrderRepositoryProvider = DoubleCheck.provider((Provider) orderRepositoryImplProvider);
      this.productRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 16);
      this.bindProductRepositoryProvider = DoubleCheck.provider((Provider) productRepositoryImplProvider);
      this.provideDepartmentDaoProvider = DoubleCheck.provider(new SwitchingProvider<DepartmentDao>(singletonCImpl, 18));
      this.departmentRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 17);
      this.bindDepartmentRepositoryProvider = DoubleCheck.provider((Provider) departmentRepositoryImplProvider);
      this.permissionRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<PermissionRepositoryImpl>(singletonCImpl, 19));
      this.providePositionDaoProvider = DoubleCheck.provider(new SwitchingProvider<PositionDao>(singletonCImpl, 21));
      this.positionRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 20);
      this.bindPositionRepositoryProvider = DoubleCheck.provider((Provider) positionRepositoryImplProvider);
      this.providePostDaoProvider = DoubleCheck.provider(new SwitchingProvider<PostDao>(singletonCImpl, 23));
      this.postRepositoryImplProvider = new SwitchingProvider<>(singletonCImpl, 22);
      this.bindPostRepositoryProvider = DoubleCheck.provider((Provider) postRepositoryImplProvider);
      this.processPostMappingRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<ProcessPostMappingRepositoryImpl>(singletonCImpl, 24));
      this.processTemplateRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<ProcessTemplateRepositoryImpl>(singletonCImpl, 25));
      this.userAuditRepositoryImplProvider = DoubleCheck.provider(new SwitchingProvider<UserAuditRepositoryImpl>(singletonCImpl, 26));
    }

    @Override
    public void injectOPMSApplication(OPMSApplication oPMSApplication) {
      injectOPMSApplication2(oPMSApplication);
    }

    @Override
    public Set<Boolean> getDisableFragmentGetContextFix() {
      return Collections.<Boolean>emptySet();
    }

    @Override
    public ActivityRetainedComponentBuilder retainedComponentBuilder() {
      return new ActivityRetainedCBuilder(singletonCImpl);
    }

    @Override
    public ServiceComponentBuilder serviceComponentBuilder() {
      return new ServiceCBuilder(singletonCImpl);
    }

    @CanIgnoreReturnValue
    private OPMSApplication injectOPMSApplication2(OPMSApplication instance) {
      OPMSApplication_MembersInjector.injectUserRepository(instance, userRepositoryImplProvider.get());
      OPMSApplication_MembersInjector.injectInitializeApp(instance);
      return instance;
    }

    private static final class SwitchingProvider<T> implements Provider<T> {
      private final SingletonCImpl singletonCImpl;

      private final int id;

      SwitchingProvider(SingletonCImpl singletonCImpl, int id) {
        this.singletonCImpl = singletonCImpl;
        this.id = id;
      }

      @SuppressWarnings("unchecked")
      @Override
      public T get() {
        switch (id) {
          case 0: // com.opms.data.repository.UserRepositoryImpl 
          return (T) new UserRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          case 1: // com.opms.data.remote.ApiService 
          return (T) NetworkModule_ProvideApiServiceFactory.provideApiService(singletonCImpl.networkModule, singletonCImpl.provideRetrofitProvider.get());

          case 2: // retrofit2.Retrofit 
          return (T) NetworkModule_ProvideRetrofitFactory.provideRetrofit(singletonCImpl.networkModule, singletonCImpl.provideOkHttpClientProvider.get());

          case 3: // okhttp3.OkHttpClient 
          return (T) NetworkModule_ProvideOkHttpClientFactory.provideOkHttpClient(singletonCImpl.networkModule, ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideLoggingInterceptorProvider.get(), singletonCImpl.provideAuthInterceptorProvider.get(), singletonCImpl.provideResponseInterceptorProvider.get());

          case 4: // okhttp3.logging.HttpLoggingInterceptor 
          return (T) NetworkModule_ProvideLoggingInterceptorFactory.provideLoggingInterceptor(singletonCImpl.networkModule);

          case 5: // com.opms.data.remote.AuthInterceptor 
          return (T) NetworkModule_ProvideAuthInterceptorFactory.provideAuthInterceptor(singletonCImpl.networkModule, singletonCImpl.providePreferencesManagerProvider.get());

          case 6: // com.opms.data.local.PreferencesManager 
          return (T) AppModule_ProvidePreferencesManagerFactory.providePreferencesManager(singletonCImpl.appModule, ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 7: // com.opms.data.remote.interceptor.ResponseInterceptor 
          return (T) NetworkModule_ProvideResponseInterceptorFactory.provideResponseInterceptor(singletonCImpl.networkModule, ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule), singletonCImpl.provideResponseConverterProvider.get());

          case 8: // retrofit2.Converter<okhttp3.ResponseBody,com.opms.data.model.response.ApiResponse<?>> 
          return (T) NetworkModule_ProvideResponseConverterFactory.provideResponseConverter(singletonCImpl.networkModule);

          case 9: // com.opms.data.repository.ComponentRepositoryImpl 
          return (T) new ComponentRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          case 10: // com.opms.data.repository.ImageUploadRepositoryImpl 
          return (T) new ImageUploadRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          case 11: // com.opms.data.repository.CustomerRepositoryImpl 
          return (T) new CustomerRepositoryImpl(singletonCImpl.provideCustomerDaoProvider.get(), singletonCImpl.provideApiServiceProvider.get());

          case 12: // com.opms.data.local.dao.CustomerDao 
          return (T) AppModule_ProvideCustomerDaoFactory.provideCustomerDao(singletonCImpl.appModule, singletonCImpl.provideAppDatabaseProvider.get());

          case 13: // com.opms.data.local.AppDatabase 
          return (T) AppModule_ProvideAppDatabaseFactory.provideAppDatabase(singletonCImpl.appModule, ApplicationContextModule_ProvideContextFactory.provideContext(singletonCImpl.applicationContextModule));

          case 14: // com.opms.data.repository.OrderRepositoryImpl 
          return (T) new OrderRepositoryImpl(singletonCImpl.provideOrderDaoProvider.get(), singletonCImpl.provideApiServiceProvider.get());

          case 15: // com.opms.data.local.dao.OrderDao 
          return (T) AppModule_ProvideOrderDaoFactory.provideOrderDao(singletonCImpl.appModule, singletonCImpl.provideAppDatabaseProvider.get());

          case 16: // com.opms.data.repository.ProductRepositoryImpl 
          return (T) new ProductRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          case 17: // com.opms.data.repository.DepartmentRepositoryImpl 
          return (T) new DepartmentRepositoryImpl(singletonCImpl.provideDepartmentDaoProvider.get(), singletonCImpl.provideApiServiceProvider.get());

          case 18: // com.opms.data.local.dao.DepartmentDao 
          return (T) AppModule_ProvideDepartmentDaoFactory.provideDepartmentDao(singletonCImpl.appModule, singletonCImpl.provideAppDatabaseProvider.get());

          case 19: // com.opms.data.repository.PermissionRepositoryImpl 
          return (T) new PermissionRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          case 20: // com.opms.data.repository.PositionRepositoryImpl 
          return (T) new PositionRepositoryImpl(singletonCImpl.providePositionDaoProvider.get(), singletonCImpl.provideApiServiceProvider.get());

          case 21: // com.opms.data.local.dao.PositionDao 
          return (T) AppModule_ProvidePositionDaoFactory.providePositionDao(singletonCImpl.appModule, singletonCImpl.provideAppDatabaseProvider.get());

          case 22: // com.opms.data.repository.PostRepositoryImpl 
          return (T) new PostRepositoryImpl(singletonCImpl.providePostDaoProvider.get(), singletonCImpl.provideApiServiceProvider.get());

          case 23: // com.opms.data.local.dao.PostDao 
          return (T) AppModule_ProvidePostDaoFactory.providePostDao(singletonCImpl.appModule, singletonCImpl.provideAppDatabaseProvider.get());

          case 24: // com.opms.data.repository.ProcessPostMappingRepositoryImpl 
          return (T) new ProcessPostMappingRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          case 25: // com.opms.data.repository.ProcessTemplateRepositoryImpl 
          return (T) new ProcessTemplateRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          case 26: // com.opms.data.repository.UserAuditRepositoryImpl 
          return (T) new UserAuditRepositoryImpl(singletonCImpl.provideApiServiceProvider.get());

          default: throw new AssertionError(id);
        }
      }
    }
  }
}
