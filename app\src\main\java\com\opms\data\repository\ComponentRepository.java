package com.opms.data.repository;

import com.opms.data.model.request.ComponentRequest;
import com.opms.data.model.response.ApiResponse;
import com.opms.data.model.response.ComponentListResponse;
import com.opms.data.model.response.ComponentResponse;

import retrofit2.Call;

public interface ComponentRepository {
    /**
     * 分页获取部件列表
     *
     * @param page    页码
     * @param size    每页大小
     * @param keyword 搜索关键字
     * @return 部件列表
     */
    Call<ApiResponse<ComponentListResponse>> getComponentList(int page, int size, String keyword);

    /**
     * 获取部件详情
     *
     * @param id 部件ID
     * @return 部件详情
     */
    Call<ApiResponse<ComponentResponse>> getComponentDetail(int id);

    /**
     * 新增部件
     *
     * @param request 部件请求
     * @return 部件响应
     */
    Call<ApiResponse<ComponentResponse>> createComponent(ComponentRequest request);

    /**
     * 修改部件
     *
     * @param id      部件ID
     * @param request 部件请求
     * @return 部件响应
     */
    Call<ApiResponse<ComponentResponse>> updateComponent(int id, ComponentRequest request);

    /**
     * 删除部件
     *
     * @param request 部件请求
     * @return 删除结果
     */
    Call<ApiResponse<Void>> deleteComponent(ComponentRequest request);

    /**
     * 检查部件编码是否存在
     *
     * @param code 部件编码
     * @return 部件响应
     */
    Call<ApiResponse<ComponentResponse>> checkComponentCodeExists(String code);
}
