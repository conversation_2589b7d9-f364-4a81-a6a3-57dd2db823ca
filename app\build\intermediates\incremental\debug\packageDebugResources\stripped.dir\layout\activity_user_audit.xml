<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background"
    tools:context=".ui.system.UserAuditActivity">

    <!-- 工具栏 -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:title="用户审核"
        app:titleTextColor="@color/white" />

    <!-- 加载进度条 -->
    <com.google.android.material.progressindicator.LinearProgressIndicator
        android:id="@+id/progress_loading"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="true"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/toolbar" />

    <!-- 滚动内容 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scroll_content"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/progress_loading">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 用户基本信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 卡片标题 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="用户基本信息"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- 用户头像和基本信息 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <!-- 用户头像 -->
                        <com.google.android.material.card.MaterialCardView
                            android:layout_width="80dp"
                            android:layout_height="80dp"
                            app:cardCornerRadius="40dp"
                            app:cardElevation="2dp">

                            <ImageView
                                android:id="@+id/iv_user_avatar"
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:scaleType="centerCrop"
                                android:src="@drawable/ic_person" />

                        </com.google.android.material.card.MaterialCardView>

                        <!-- 基本信息 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="16dp"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- 用户名 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="80dp"
                                    android:layout_height="wrap_content"
                                    android:text="用户名："
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_username"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    android:textStyle="bold"
                                    tools:text="user001" />

                            </LinearLayout>

                            <!-- 姓名 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="80dp"
                                    android:layout_height="wrap_content"
                                    android:text="姓名："
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_name"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    tools:text="张三" />

                            </LinearLayout>

                            <!-- 性别 -->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="4dp"
                                android:orientation="horizontal">

                                <TextView
                                    android:layout_width="80dp"
                                    android:layout_height="wrap_content"
                                    android:text="性别："
                                    android:textColor="@color/text_secondary"
                                    android:textSize="14sp" />

                                <TextView
                                    android:id="@+id/tv_gender"
                                    android:layout_width="0dp"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    android:textColor="@color/text_primary"
                                    android:textSize="14sp"
                                    tools:text="男" />

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 详细信息 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:orientation="vertical">

                        <!-- 出生日期 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:text="出生日期："
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_birth_date"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                tools:text="1990-01-01" />

                        </LinearLayout>

                        <!-- 身份证号 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:text="身份证号："
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_id_card"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                tools:text="110101199001010001" />

                        </LinearLayout>

                        <!-- 手机号 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:text="手机号："
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_phone"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                tools:text="13888888888" />

                        </LinearLayout>

                        <!-- 注册时间 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <TextView
                                android:layout_width="100dp"
                                android:layout_height="wrap_content"
                                android:text="注册时间："
                                android:textColor="@color/text_secondary"
                                android:textSize="14sp" />

                            <TextView
                                android:id="@+id/tv_register_time"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:textColor="@color/text_primary"
                                android:textSize="14sp"
                                tools:text="2024-12-01 10:30:00" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 审核结果卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_audit_result"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardElevation="2dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <!-- 卡片标题 -->
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        android:text="审核结果"
                        android:textColor="@color/text_primary"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                    <!-- 审核结果选择 -->
                    <com.google.android.material.chip.ChipGroup
                        android:id="@+id/chip_group_audit_result"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginBottom="16dp"
                        app:selectionRequired="true"
                        app:singleSelection="true">

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_approve"
                            style="@style/Widget.MaterialComponents.Chip.Choice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="通过" />

                        <com.google.android.material.chip.Chip
                            android:id="@+id/chip_reject"
                            style="@style/Widget.MaterialComponents.Chip.Choice"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="拒绝" />

                    </com.google.android.material.chip.ChipGroup>

                    <!-- 备注信息 -->
                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_remark"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="备注信息">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_remark"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textMultiLine"
                            android:lines="3"
                            android:maxLines="5" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 用户分配信息卡片 -->
            <include
                android:id="@+id/include_user_assignment"
                layout="@layout/layout_user_assignment"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <!-- 提交按钮 -->
            <com.google.android.material.button.MaterialButton
                android:id="@+id/btn_submit"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="32dp"
                android:padding="16dp"
                android:text="提交审核"
                android:textSize="16sp" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>